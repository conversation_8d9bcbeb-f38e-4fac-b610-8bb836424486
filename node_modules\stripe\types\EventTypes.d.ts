// File generated from our OpenAPI spec

declare module 'stripe' {
  namespace Stripe {
    export type Event =
      | AccountApplicationAuthorizedEvent
      | AccountApplicationDeauthorizedEvent
      | AccountExternalAccountCreatedEvent
      | AccountExternalAccountDeletedEvent
      | AccountExternalAccountUpdatedEvent
      | AccountUpdatedEvent
      | ApplicationFeeCreatedEvent
      | ApplicationFeeRefundUpdatedEvent
      | ApplicationFeeRefundedEvent
      | BalanceAvailableEvent
      | BillingAlertTriggeredEvent
      | BillingCreditBalanceTransactionCreatedEvent
      | BillingCreditGrantCreatedEvent
      | BillingCreditGrantUpdatedEvent
      | BillingMeterCreatedEvent
      | BillingMeterDeactivatedEvent
      | BillingMeterReactivatedEvent
      | BillingMeterUpdatedEvent
      | BillingPortalConfigurationCreatedEvent
      | BillingPortalConfigurationUpdatedEvent
      | BillingPortalSessionCreatedEvent
      | CapabilityUpdatedEvent
      | CashBalanceFundsAvailableEvent
      | ChargeCapturedEvent
      | ChargeDisputeClosedEvent
      | ChargeDisputeCreatedEvent
      | ChargeDisputeFundsReinstatedEvent
      | ChargeDisputeFundsWithdrawnEvent
      | ChargeDisputeUpdatedEvent
      | ChargeExpiredEvent
      | ChargeFailedEvent
      | ChargePendingEvent
      | ChargeRefundUpdatedEvent
      | ChargeRefundedEvent
      | ChargeSucceededEvent
      | ChargeUpdatedEvent
      | CheckoutSessionAsyncPaymentFailedEvent
      | CheckoutSessionAsyncPaymentSucceededEvent
      | CheckoutSessionCompletedEvent
      | CheckoutSessionExpiredEvent
      | ClimateOrderCanceledEvent
      | ClimateOrderCreatedEvent
      | ClimateOrderDelayedEvent
      | ClimateOrderDeliveredEvent
      | ClimateOrderProductSubstitutedEvent
      | ClimateProductCreatedEvent
      | ClimateProductPricingUpdatedEvent
      | CouponCreatedEvent
      | CouponDeletedEvent
      | CouponUpdatedEvent
      | CreditNoteCreatedEvent
      | CreditNoteUpdatedEvent
      | CreditNoteVoidedEvent
      | CustomerCreatedEvent
      | CustomerDeletedEvent
      | CustomerDiscountCreatedEvent
      | CustomerDiscountDeletedEvent
      | CustomerDiscountUpdatedEvent
      | CustomerSourceCreatedEvent
      | CustomerSourceDeletedEvent
      | CustomerSourceExpiringEvent
      | CustomerSourceUpdatedEvent
      | CustomerSubscriptionCreatedEvent
      | CustomerSubscriptionDeletedEvent
      | CustomerSubscriptionPausedEvent
      | CustomerSubscriptionPendingUpdateAppliedEvent
      | CustomerSubscriptionPendingUpdateExpiredEvent
      | CustomerSubscriptionResumedEvent
      | CustomerSubscriptionTrialWillEndEvent
      | CustomerSubscriptionUpdatedEvent
      | CustomerTaxIdCreatedEvent
      | CustomerTaxIdDeletedEvent
      | CustomerTaxIdUpdatedEvent
      | CustomerUpdatedEvent
      | CustomerCashBalanceTransactionCreatedEvent
      | EntitlementsActiveEntitlementSummaryUpdatedEvent
      | FileCreatedEvent
      | FinancialConnectionsAccountCreatedEvent
      | FinancialConnectionsAccountDeactivatedEvent
      | FinancialConnectionsAccountDisconnectedEvent
      | FinancialConnectionsAccountReactivatedEvent
      | FinancialConnectionsAccountRefreshedBalanceEvent
      | FinancialConnectionsAccountRefreshedOwnershipEvent
      | FinancialConnectionsAccountRefreshedTransactionsEvent
      | IdentityVerificationSessionCanceledEvent
      | IdentityVerificationSessionCreatedEvent
      | IdentityVerificationSessionProcessingEvent
      | IdentityVerificationSessionRedactedEvent
      | IdentityVerificationSessionRequiresInputEvent
      | IdentityVerificationSessionVerifiedEvent
      | InvoiceCreatedEvent
      | InvoiceDeletedEvent
      | InvoiceFinalizationFailedEvent
      | InvoiceFinalizedEvent
      | InvoiceMarkedUncollectibleEvent
      | InvoiceOverdueEvent
      | InvoiceOverpaidEvent
      | InvoicePaidEvent
      | InvoicePaymentActionRequiredEvent
      | InvoicePaymentFailedEvent
      | InvoicePaymentSucceededEvent
      | InvoiceSentEvent
      | InvoiceUpcomingEvent
      | InvoiceUpdatedEvent
      | InvoiceVoidedEvent
      | InvoiceWillBeDueEvent
      | InvoicePaymentPaidEvent
      | InvoiceItemCreatedEvent
      | InvoiceItemDeletedEvent
      | IssuingAuthorizationCreatedEvent
      | IssuingAuthorizationRequestEvent
      | IssuingAuthorizationUpdatedEvent
      | IssuingCardCreatedEvent
      | IssuingCardUpdatedEvent
      | IssuingCardholderCreatedEvent
      | IssuingCardholderUpdatedEvent
      | IssuingDisputeClosedEvent
      | IssuingDisputeCreatedEvent
      | IssuingDisputeFundsReinstatedEvent
      | IssuingDisputeFundsRescindedEvent
      | IssuingDisputeSubmittedEvent
      | IssuingDisputeUpdatedEvent
      | IssuingPersonalizationDesignActivatedEvent
      | IssuingPersonalizationDesignDeactivatedEvent
      | IssuingPersonalizationDesignRejectedEvent
      | IssuingPersonalizationDesignUpdatedEvent
      | IssuingTokenCreatedEvent
      | IssuingTokenUpdatedEvent
      | IssuingTransactionCreatedEvent
      | IssuingTransactionPurchaseDetailsReceiptUpdatedEvent
      | IssuingTransactionUpdatedEvent
      | MandateUpdatedEvent
      | PaymentIntentAmountCapturableUpdatedEvent
      | PaymentIntentCanceledEvent
      | PaymentIntentCreatedEvent
      | PaymentIntentPartiallyFundedEvent
      | PaymentIntentPaymentFailedEvent
      | PaymentIntentProcessingEvent
      | PaymentIntentRequiresActionEvent
      | PaymentIntentSucceededEvent
      | PaymentLinkCreatedEvent
      | PaymentLinkUpdatedEvent
      | PaymentMethodAttachedEvent
      | PaymentMethodAutomaticallyUpdatedEvent
      | PaymentMethodDetachedEvent
      | PaymentMethodUpdatedEvent
      | PayoutCanceledEvent
      | PayoutCreatedEvent
      | PayoutFailedEvent
      | PayoutPaidEvent
      | PayoutReconciliationCompletedEvent
      | PayoutUpdatedEvent
      | PersonCreatedEvent
      | PersonDeletedEvent
      | PersonUpdatedEvent
      | PlanCreatedEvent
      | PlanDeletedEvent
      | PlanUpdatedEvent
      | PriceCreatedEvent
      | PriceDeletedEvent
      | PriceUpdatedEvent
      | ProductCreatedEvent
      | ProductDeletedEvent
      | ProductUpdatedEvent
      | PromotionCodeCreatedEvent
      | PromotionCodeUpdatedEvent
      | QuoteAcceptedEvent
      | QuoteCanceledEvent
      | QuoteCreatedEvent
      | QuoteFinalizedEvent
      | RadarEarlyFraudWarningCreatedEvent
      | RadarEarlyFraudWarningUpdatedEvent
      | RefundCreatedEvent
      | RefundFailedEvent
      | RefundUpdatedEvent
      | ReportingReportRunFailedEvent
      | ReportingReportRunSucceededEvent
      | ReportingReportTypeUpdatedEvent
      | ReviewClosedEvent
      | ReviewOpenedEvent
      | SetupIntentCanceledEvent
      | SetupIntentCreatedEvent
      | SetupIntentRequiresActionEvent
      | SetupIntentSetupFailedEvent
      | SetupIntentSucceededEvent
      | SigmaScheduledQueryRunCreatedEvent
      | SourceCanceledEvent
      | SourceChargeableEvent
      | SourceFailedEvent
      | SourceMandateNotificationEvent
      | SourceRefundAttributesRequiredEvent
      | SourceTransactionCreatedEvent
      | SourceTransactionUpdatedEvent
      | SubscriptionScheduleAbortedEvent
      | SubscriptionScheduleCanceledEvent
      | SubscriptionScheduleCompletedEvent
      | SubscriptionScheduleCreatedEvent
      | SubscriptionScheduleExpiringEvent
      | SubscriptionScheduleReleasedEvent
      | SubscriptionScheduleUpdatedEvent
      | TaxSettingsUpdatedEvent
      | TaxRateCreatedEvent
      | TaxRateUpdatedEvent
      | TerminalReaderActionFailedEvent
      | TerminalReaderActionSucceededEvent
      | TerminalReaderActionUpdatedEvent
      | TestHelpersTestClockAdvancingEvent
      | TestHelpersTestClockCreatedEvent
      | TestHelpersTestClockDeletedEvent
      | TestHelpersTestClockInternalFailureEvent
      | TestHelpersTestClockReadyEvent
      | TopupCanceledEvent
      | TopupCreatedEvent
      | TopupFailedEvent
      | TopupReversedEvent
      | TopupSucceededEvent
      | TransferCreatedEvent
      | TransferReversedEvent
      | TransferUpdatedEvent
      | TreasuryCreditReversalCreatedEvent
      | TreasuryCreditReversalPostedEvent
      | TreasuryDebitReversalCompletedEvent
      | TreasuryDebitReversalCreatedEvent
      | TreasuryDebitReversalInitialCreditGrantedEvent
      | TreasuryFinancialAccountClosedEvent
      | TreasuryFinancialAccountCreatedEvent
      | TreasuryFinancialAccountFeaturesStatusUpdatedEvent
      | TreasuryInboundTransferCanceledEvent
      | TreasuryInboundTransferCreatedEvent
      | TreasuryInboundTransferFailedEvent
      | TreasuryInboundTransferSucceededEvent
      | TreasuryOutboundPaymentCanceledEvent
      | TreasuryOutboundPaymentCreatedEvent
      | TreasuryOutboundPaymentExpectedArrivalDateUpdatedEvent
      | TreasuryOutboundPaymentFailedEvent
      | TreasuryOutboundPaymentPostedEvent
      | TreasuryOutboundPaymentReturnedEvent
      | TreasuryOutboundPaymentTrackingDetailsUpdatedEvent
      | TreasuryOutboundTransferCanceledEvent
      | TreasuryOutboundTransferCreatedEvent
      | TreasuryOutboundTransferExpectedArrivalDateUpdatedEvent
      | TreasuryOutboundTransferFailedEvent
      | TreasuryOutboundTransferPostedEvent
      | TreasuryOutboundTransferReturnedEvent
      | TreasuryOutboundTransferTrackingDetailsUpdatedEvent
      | TreasuryReceivedCreditCreatedEvent
      | TreasuryReceivedCreditFailedEvent
      | TreasuryReceivedCreditSucceededEvent
      | TreasuryReceivedDebitCreatedEvent;
  }

  namespace Stripe {
    /**
     * Occurs whenever a user authorizes an application. Sent to the related application only.
     */
    interface AccountApplicationAuthorizedEvent extends EventBase {
      type: 'account.application.authorized';
      data: AccountApplicationAuthorizedEvent.Data;
    }

    namespace AccountApplicationAuthorizedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Application;

        previous_attributes?: Partial<Stripe.Application>;
      }
    }

    /**
     * Occurs whenever a user deauthorizes an application. Sent to the related application only.
     */
    interface AccountApplicationDeauthorizedEvent extends EventBase {
      type: 'account.application.deauthorized';
      data: AccountApplicationDeauthorizedEvent.Data;
    }

    namespace AccountApplicationDeauthorizedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Application;

        previous_attributes?: Partial<Stripe.Application>;
      }
    }

    /**
     * Occurs whenever an external account is created.
     */
    interface AccountExternalAccountCreatedEvent extends EventBase {
      type: 'account.external_account.created';
      data: AccountExternalAccountCreatedEvent.Data;
    }

    namespace AccountExternalAccountCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.ExternalAccount;

        previous_attributes?: Partial<Stripe.ExternalAccount>;
      }
    }

    /**
     * Occurs whenever an external account is deleted.
     */
    interface AccountExternalAccountDeletedEvent extends EventBase {
      type: 'account.external_account.deleted';
      data: AccountExternalAccountDeletedEvent.Data;
    }

    namespace AccountExternalAccountDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.ExternalAccount;

        previous_attributes?: Partial<Stripe.ExternalAccount>;
      }
    }

    /**
     * Occurs whenever an external account is updated.
     */
    interface AccountExternalAccountUpdatedEvent extends EventBase {
      type: 'account.external_account.updated';
      data: AccountExternalAccountUpdatedEvent.Data;
    }

    namespace AccountExternalAccountUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.ExternalAccount;

        previous_attributes?: Partial<Stripe.ExternalAccount>;
      }
    }

    /**
     * Occurs whenever an account status or property has changed.
     */
    interface AccountUpdatedEvent extends EventBase {
      type: 'account.updated';
      data: AccountUpdatedEvent.Data;
    }

    namespace AccountUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Account;

        previous_attributes?: Partial<Stripe.Account>;
      }
    }

    /**
     * Occurs whenever an application fee is created on a charge.
     */
    interface ApplicationFeeCreatedEvent extends EventBase {
      type: 'application_fee.created';
      data: ApplicationFeeCreatedEvent.Data;
    }

    namespace ApplicationFeeCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.ApplicationFee;

        previous_attributes?: Partial<Stripe.ApplicationFee>;
      }
    }

    /**
     * Occurs whenever an application fee refund is updated.
     */
    interface ApplicationFeeRefundUpdatedEvent extends EventBase {
      type: 'application_fee.refund.updated';
      data: ApplicationFeeRefundUpdatedEvent.Data;
    }

    namespace ApplicationFeeRefundUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.FeeRefund;

        previous_attributes?: Partial<Stripe.FeeRefund>;
      }
    }

    /**
     * Occurs whenever an application fee is refunded, whether from refunding a charge or from [refunding the application fee directly](#fee_refunds). This includes partial refunds.
     */
    interface ApplicationFeeRefundedEvent extends EventBase {
      type: 'application_fee.refunded';
      data: ApplicationFeeRefundedEvent.Data;
    }

    namespace ApplicationFeeRefundedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.ApplicationFee;

        previous_attributes?: Partial<Stripe.ApplicationFee>;
      }
    }

    /**
     * Occurs whenever your Stripe balance has been updated (e.g., when a charge is available to be paid out). By default, Stripe automatically transfers funds in your balance to your bank account on a daily basis. This event is not fired for negative transactions.
     */
    interface BalanceAvailableEvent extends EventBase {
      type: 'balance.available';
      data: BalanceAvailableEvent.Data;
    }

    namespace BalanceAvailableEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Balance;

        previous_attributes?: Partial<Stripe.Balance>;
      }
    }

    /**
     * Occurs whenever your custom alert threshold is met.
     */
    interface BillingAlertTriggeredEvent extends EventBase {
      type: 'billing.alert.triggered';
      data: BillingAlertTriggeredEvent.Data;
    }

    namespace BillingAlertTriggeredEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Billing.AlertTriggered;

        previous_attributes?: Partial<Stripe.Billing.AlertTriggered>;
      }
    }

    /**
     * Occurs when a credit balance transaction is created
     */
    interface BillingCreditBalanceTransactionCreatedEvent extends EventBase {
      type: 'billing.credit_balance_transaction.created';
      data: BillingCreditBalanceTransactionCreatedEvent.Data;
    }

    namespace BillingCreditBalanceTransactionCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Billing.CreditBalanceTransaction;

        previous_attributes?: Partial<Stripe.Billing.CreditBalanceTransaction>;
      }
    }

    /**
     * Occurs when a credit grant is created
     */
    interface BillingCreditGrantCreatedEvent extends EventBase {
      type: 'billing.credit_grant.created';
      data: BillingCreditGrantCreatedEvent.Data;
    }

    namespace BillingCreditGrantCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Billing.CreditGrant;

        previous_attributes?: Partial<Stripe.Billing.CreditGrant>;
      }
    }

    /**
     * Occurs when a credit grant is updated
     */
    interface BillingCreditGrantUpdatedEvent extends EventBase {
      type: 'billing.credit_grant.updated';
      data: BillingCreditGrantUpdatedEvent.Data;
    }

    namespace BillingCreditGrantUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Billing.CreditGrant;

        previous_attributes?: Partial<Stripe.Billing.CreditGrant>;
      }
    }

    /**
     * Occurs when a meter is created
     */
    interface BillingMeterCreatedEvent extends EventBase {
      type: 'billing.meter.created';
      data: BillingMeterCreatedEvent.Data;
    }

    namespace BillingMeterCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Billing.Meter;

        previous_attributes?: Partial<Stripe.Billing.Meter>;
      }
    }

    /**
     * Occurs when a meter is deactivated
     */
    interface BillingMeterDeactivatedEvent extends EventBase {
      type: 'billing.meter.deactivated';
      data: BillingMeterDeactivatedEvent.Data;
    }

    namespace BillingMeterDeactivatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Billing.Meter;

        previous_attributes?: Partial<Stripe.Billing.Meter>;
      }
    }

    /**
     * Occurs when a meter is reactivated
     */
    interface BillingMeterReactivatedEvent extends EventBase {
      type: 'billing.meter.reactivated';
      data: BillingMeterReactivatedEvent.Data;
    }

    namespace BillingMeterReactivatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Billing.Meter;

        previous_attributes?: Partial<Stripe.Billing.Meter>;
      }
    }

    /**
     * Occurs when a meter is updated
     */
    interface BillingMeterUpdatedEvent extends EventBase {
      type: 'billing.meter.updated';
      data: BillingMeterUpdatedEvent.Data;
    }

    namespace BillingMeterUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Billing.Meter;

        previous_attributes?: Partial<Stripe.Billing.Meter>;
      }
    }

    /**
     * Occurs whenever a portal configuration is created.
     */
    interface BillingPortalConfigurationCreatedEvent extends EventBase {
      type: 'billing_portal.configuration.created';
      data: BillingPortalConfigurationCreatedEvent.Data;
    }

    namespace BillingPortalConfigurationCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.BillingPortal.Configuration;

        previous_attributes?: Partial<Stripe.BillingPortal.Configuration>;
      }
    }

    /**
     * Occurs whenever a portal configuration is updated.
     */
    interface BillingPortalConfigurationUpdatedEvent extends EventBase {
      type: 'billing_portal.configuration.updated';
      data: BillingPortalConfigurationUpdatedEvent.Data;
    }

    namespace BillingPortalConfigurationUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.BillingPortal.Configuration;

        previous_attributes?: Partial<Stripe.BillingPortal.Configuration>;
      }
    }

    /**
     * Occurs whenever a portal session is created.
     */
    interface BillingPortalSessionCreatedEvent extends EventBase {
      type: 'billing_portal.session.created';
      data: BillingPortalSessionCreatedEvent.Data;
    }

    namespace BillingPortalSessionCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.BillingPortal.Session;

        previous_attributes?: Partial<Stripe.BillingPortal.Session>;
      }
    }

    /**
     * Occurs whenever a capability has new requirements or a new status.
     */
    interface CapabilityUpdatedEvent extends EventBase {
      type: 'capability.updated';
      data: CapabilityUpdatedEvent.Data;
    }

    namespace CapabilityUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Capability;

        previous_attributes?: Partial<Stripe.Capability>;
      }
    }

    /**
     * Occurs whenever there is a positive remaining cash balance after Stripe automatically reconciles new funds into the cash balance. If you enabled manual reconciliation, this webhook will fire whenever there are new funds into the cash balance.
     */
    interface CashBalanceFundsAvailableEvent extends EventBase {
      type: 'cash_balance.funds_available';
      data: CashBalanceFundsAvailableEvent.Data;
    }

    namespace CashBalanceFundsAvailableEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.CashBalance;

        previous_attributes?: Partial<Stripe.CashBalance>;
      }
    }

    /**
     * Occurs whenever a previously uncaptured charge is captured.
     */
    interface ChargeCapturedEvent extends EventBase {
      type: 'charge.captured';
      data: ChargeCapturedEvent.Data;
    }

    namespace ChargeCapturedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Charge;

        previous_attributes?: Partial<Stripe.Charge>;
      }
    }

    /**
     * Occurs when a dispute is closed and the dispute status changes to `lost`, `warning_closed`, or `won`.
     */
    interface ChargeDisputeClosedEvent extends EventBase {
      type: 'charge.dispute.closed';
      data: ChargeDisputeClosedEvent.Data;
    }

    namespace ChargeDisputeClosedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Dispute;

        previous_attributes?: Partial<Stripe.Dispute>;
      }
    }

    /**
     * Occurs whenever a customer disputes a charge with their bank.
     */
    interface ChargeDisputeCreatedEvent extends EventBase {
      type: 'charge.dispute.created';
      data: ChargeDisputeCreatedEvent.Data;
    }

    namespace ChargeDisputeCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Dispute;

        previous_attributes?: Partial<Stripe.Dispute>;
      }
    }

    /**
     * Occurs when funds are reinstated to your account after a dispute is closed. This includes [partially refunded payments](https://docs.stripe.com/disputes#disputes-on-partially-refunded-payments).
     */
    interface ChargeDisputeFundsReinstatedEvent extends EventBase {
      type: 'charge.dispute.funds_reinstated';
      data: ChargeDisputeFundsReinstatedEvent.Data;
    }

    namespace ChargeDisputeFundsReinstatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Dispute;

        previous_attributes?: Partial<Stripe.Dispute>;
      }
    }

    /**
     * Occurs when funds are removed from your account due to a dispute.
     */
    interface ChargeDisputeFundsWithdrawnEvent extends EventBase {
      type: 'charge.dispute.funds_withdrawn';
      data: ChargeDisputeFundsWithdrawnEvent.Data;
    }

    namespace ChargeDisputeFundsWithdrawnEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Dispute;

        previous_attributes?: Partial<Stripe.Dispute>;
      }
    }

    /**
     * Occurs when the dispute is updated (usually with evidence).
     */
    interface ChargeDisputeUpdatedEvent extends EventBase {
      type: 'charge.dispute.updated';
      data: ChargeDisputeUpdatedEvent.Data;
    }

    namespace ChargeDisputeUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Dispute;

        previous_attributes?: Partial<Stripe.Dispute>;
      }
    }

    /**
     * Occurs whenever an uncaptured charge expires.
     */
    interface ChargeExpiredEvent extends EventBase {
      type: 'charge.expired';
      data: ChargeExpiredEvent.Data;
    }

    namespace ChargeExpiredEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Charge;

        previous_attributes?: Partial<Stripe.Charge>;
      }
    }

    /**
     * Occurs whenever a failed charge attempt occurs.
     */
    interface ChargeFailedEvent extends EventBase {
      type: 'charge.failed';
      data: ChargeFailedEvent.Data;
    }

    namespace ChargeFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Charge;

        previous_attributes?: Partial<Stripe.Charge>;
      }
    }

    /**
     * Occurs whenever a pending charge is created.
     */
    interface ChargePendingEvent extends EventBase {
      type: 'charge.pending';
      data: ChargePendingEvent.Data;
    }

    namespace ChargePendingEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Charge;

        previous_attributes?: Partial<Stripe.Charge>;
      }
    }

    /**
     * Occurs whenever a refund is updated on selected payment methods. For updates on all refunds, listen to `refund.updated` instead.
     */
    interface ChargeRefundUpdatedEvent extends EventBase {
      type: 'charge.refund.updated';
      data: ChargeRefundUpdatedEvent.Data;
    }

    namespace ChargeRefundUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Refund;

        previous_attributes?: Partial<Stripe.Refund>;
      }
    }

    /**
     * Occurs whenever a charge is refunded, including partial refunds. Listen to `refund.created` for information about the refund.
     */
    interface ChargeRefundedEvent extends EventBase {
      type: 'charge.refunded';
      data: ChargeRefundedEvent.Data;
    }

    namespace ChargeRefundedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Charge;

        previous_attributes?: Partial<Stripe.Charge>;
      }
    }

    /**
     * Occurs whenever a charge is successful.
     */
    interface ChargeSucceededEvent extends EventBase {
      type: 'charge.succeeded';
      data: ChargeSucceededEvent.Data;
    }

    namespace ChargeSucceededEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Charge;

        previous_attributes?: Partial<Stripe.Charge>;
      }
    }

    /**
     * Occurs whenever a charge description or metadata is updated, or upon an asynchronous capture.
     */
    interface ChargeUpdatedEvent extends EventBase {
      type: 'charge.updated';
      data: ChargeUpdatedEvent.Data;
    }

    namespace ChargeUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Charge;

        previous_attributes?: Partial<Stripe.Charge>;
      }
    }

    /**
     * Occurs when a payment intent using a delayed payment method fails.
     */
    interface CheckoutSessionAsyncPaymentFailedEvent extends EventBase {
      type: 'checkout.session.async_payment_failed';
      data: CheckoutSessionAsyncPaymentFailedEvent.Data;
    }

    namespace CheckoutSessionAsyncPaymentFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Checkout.Session;

        previous_attributes?: Partial<Stripe.Checkout.Session>;
      }
    }

    /**
     * Occurs when a payment intent using a delayed payment method finally succeeds.
     */
    interface CheckoutSessionAsyncPaymentSucceededEvent extends EventBase {
      type: 'checkout.session.async_payment_succeeded';
      data: CheckoutSessionAsyncPaymentSucceededEvent.Data;
    }

    namespace CheckoutSessionAsyncPaymentSucceededEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Checkout.Session;

        previous_attributes?: Partial<Stripe.Checkout.Session>;
      }
    }

    /**
     * Occurs when a Checkout Session has been successfully completed.
     */
    interface CheckoutSessionCompletedEvent extends EventBase {
      type: 'checkout.session.completed';
      data: CheckoutSessionCompletedEvent.Data;
    }

    namespace CheckoutSessionCompletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Checkout.Session;

        previous_attributes?: Partial<Stripe.Checkout.Session>;
      }
    }

    /**
     * Occurs when a Checkout Session is expired.
     */
    interface CheckoutSessionExpiredEvent extends EventBase {
      type: 'checkout.session.expired';
      data: CheckoutSessionExpiredEvent.Data;
    }

    namespace CheckoutSessionExpiredEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Checkout.Session;

        previous_attributes?: Partial<Stripe.Checkout.Session>;
      }
    }

    /**
     * Occurs when a Climate order is canceled.
     */
    interface ClimateOrderCanceledEvent extends EventBase {
      type: 'climate.order.canceled';
      data: ClimateOrderCanceledEvent.Data;
    }

    namespace ClimateOrderCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Climate.Order;

        previous_attributes?: Partial<Stripe.Climate.Order>;
      }
    }

    /**
     * Occurs when a Climate order is created.
     */
    interface ClimateOrderCreatedEvent extends EventBase {
      type: 'climate.order.created';
      data: ClimateOrderCreatedEvent.Data;
    }

    namespace ClimateOrderCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Climate.Order;

        previous_attributes?: Partial<Stripe.Climate.Order>;
      }
    }

    /**
     * Occurs when a Climate order is delayed.
     */
    interface ClimateOrderDelayedEvent extends EventBase {
      type: 'climate.order.delayed';
      data: ClimateOrderDelayedEvent.Data;
    }

    namespace ClimateOrderDelayedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Climate.Order;

        previous_attributes?: Partial<Stripe.Climate.Order>;
      }
    }

    /**
     * Occurs when a Climate order is delivered.
     */
    interface ClimateOrderDeliveredEvent extends EventBase {
      type: 'climate.order.delivered';
      data: ClimateOrderDeliveredEvent.Data;
    }

    namespace ClimateOrderDeliveredEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Climate.Order;

        previous_attributes?: Partial<Stripe.Climate.Order>;
      }
    }

    /**
     * Occurs when a Climate order's product is substituted for another.
     */
    interface ClimateOrderProductSubstitutedEvent extends EventBase {
      type: 'climate.order.product_substituted';
      data: ClimateOrderProductSubstitutedEvent.Data;
    }

    namespace ClimateOrderProductSubstitutedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Climate.Order;

        previous_attributes?: Partial<Stripe.Climate.Order>;
      }
    }

    /**
     * Occurs when a Climate product is created.
     */
    interface ClimateProductCreatedEvent extends EventBase {
      type: 'climate.product.created';
      data: ClimateProductCreatedEvent.Data;
    }

    namespace ClimateProductCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Climate.Product;

        previous_attributes?: Partial<Stripe.Climate.Product>;
      }
    }

    /**
     * Occurs when a Climate product is updated.
     */
    interface ClimateProductPricingUpdatedEvent extends EventBase {
      type: 'climate.product.pricing_updated';
      data: ClimateProductPricingUpdatedEvent.Data;
    }

    namespace ClimateProductPricingUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Climate.Product;

        previous_attributes?: Partial<Stripe.Climate.Product>;
      }
    }

    /**
     * Occurs whenever a coupon is created.
     */
    interface CouponCreatedEvent extends EventBase {
      type: 'coupon.created';
      data: CouponCreatedEvent.Data;
    }

    namespace CouponCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Coupon;

        previous_attributes?: Partial<Stripe.Coupon>;
      }
    }

    /**
     * Occurs whenever a coupon is deleted.
     */
    interface CouponDeletedEvent extends EventBase {
      type: 'coupon.deleted';
      data: CouponDeletedEvent.Data;
    }

    namespace CouponDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Coupon;

        previous_attributes?: Partial<Stripe.Coupon>;
      }
    }

    /**
     * Occurs whenever a coupon is updated.
     */
    interface CouponUpdatedEvent extends EventBase {
      type: 'coupon.updated';
      data: CouponUpdatedEvent.Data;
    }

    namespace CouponUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Coupon;

        previous_attributes?: Partial<Stripe.Coupon>;
      }
    }

    /**
     * Occurs whenever a credit note is created.
     */
    interface CreditNoteCreatedEvent extends EventBase {
      type: 'credit_note.created';
      data: CreditNoteCreatedEvent.Data;
    }

    namespace CreditNoteCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.CreditNote;

        previous_attributes?: Partial<Stripe.CreditNote>;
      }
    }

    /**
     * Occurs whenever a credit note is updated.
     */
    interface CreditNoteUpdatedEvent extends EventBase {
      type: 'credit_note.updated';
      data: CreditNoteUpdatedEvent.Data;
    }

    namespace CreditNoteUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.CreditNote;

        previous_attributes?: Partial<Stripe.CreditNote>;
      }
    }

    /**
     * Occurs whenever a credit note is voided.
     */
    interface CreditNoteVoidedEvent extends EventBase {
      type: 'credit_note.voided';
      data: CreditNoteVoidedEvent.Data;
    }

    namespace CreditNoteVoidedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.CreditNote;

        previous_attributes?: Partial<Stripe.CreditNote>;
      }
    }

    /**
     * Occurs whenever a new customer is created.
     */
    interface CustomerCreatedEvent extends EventBase {
      type: 'customer.created';
      data: CustomerCreatedEvent.Data;
    }

    namespace CustomerCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Customer;

        previous_attributes?: Partial<Stripe.Customer>;
      }
    }

    /**
     * Occurs whenever a customer is deleted.
     */
    interface CustomerDeletedEvent extends EventBase {
      type: 'customer.deleted';
      data: CustomerDeletedEvent.Data;
    }

    namespace CustomerDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Customer;

        previous_attributes?: Partial<Stripe.Customer>;
      }
    }

    /**
     * Occurs whenever a coupon is attached to a customer.
     */
    interface CustomerDiscountCreatedEvent extends EventBase {
      type: 'customer.discount.created';
      data: CustomerDiscountCreatedEvent.Data;
    }

    namespace CustomerDiscountCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Discount;

        previous_attributes?: Partial<Stripe.Discount>;
      }
    }

    /**
     * Occurs whenever a coupon is removed from a customer.
     */
    interface CustomerDiscountDeletedEvent extends EventBase {
      type: 'customer.discount.deleted';
      data: CustomerDiscountDeletedEvent.Data;
    }

    namespace CustomerDiscountDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Discount;

        previous_attributes?: Partial<Stripe.Discount>;
      }
    }

    /**
     * Occurs whenever a customer is switched from one coupon to another.
     */
    interface CustomerDiscountUpdatedEvent extends EventBase {
      type: 'customer.discount.updated';
      data: CustomerDiscountUpdatedEvent.Data;
    }

    namespace CustomerDiscountUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Discount;

        previous_attributes?: Partial<Stripe.Discount>;
      }
    }

    /**
     * Occurs whenever a new source is created for a customer.
     */
    interface CustomerSourceCreatedEvent extends EventBase {
      type: 'customer.source.created';
      data: CustomerSourceCreatedEvent.Data;
    }

    namespace CustomerSourceCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.CustomerSource;

        previous_attributes?: Partial<Stripe.CustomerSource>;
      }
    }

    /**
     * Occurs whenever a source is removed from a customer.
     */
    interface CustomerSourceDeletedEvent extends EventBase {
      type: 'customer.source.deleted';
      data: CustomerSourceDeletedEvent.Data;
    }

    namespace CustomerSourceDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.CustomerSource;

        previous_attributes?: Partial<Stripe.CustomerSource>;
      }
    }

    /**
     * Occurs whenever a card or source will expire at the end of the month. This event only works with legacy integrations using Card or Source objects. If you use the PaymentMethod API, this event won't occur.
     */
    interface CustomerSourceExpiringEvent extends EventBase {
      type: 'customer.source.expiring';
      data: CustomerSourceExpiringEvent.Data;
    }

    namespace CustomerSourceExpiringEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.CustomerSource;

        previous_attributes?: Partial<Stripe.CustomerSource>;
      }
    }

    /**
     * Occurs whenever a source's details are changed.
     */
    interface CustomerSourceUpdatedEvent extends EventBase {
      type: 'customer.source.updated';
      data: CustomerSourceUpdatedEvent.Data;
    }

    namespace CustomerSourceUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.CustomerSource;

        previous_attributes?: Partial<Stripe.CustomerSource>;
      }
    }

    /**
     * Occurs whenever a customer is signed up for a new plan.
     */
    interface CustomerSubscriptionCreatedEvent extends EventBase {
      type: 'customer.subscription.created';
      data: CustomerSubscriptionCreatedEvent.Data;
    }

    namespace CustomerSubscriptionCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Subscription;

        previous_attributes?: Partial<Stripe.Subscription>;
      }
    }

    /**
     * Occurs whenever a customer's subscription ends.
     */
    interface CustomerSubscriptionDeletedEvent extends EventBase {
      type: 'customer.subscription.deleted';
      data: CustomerSubscriptionDeletedEvent.Data;
    }

    namespace CustomerSubscriptionDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Subscription;

        previous_attributes?: Partial<Stripe.Subscription>;
      }
    }

    /**
     * Occurs whenever a customer's subscription is paused. Only applies when subscriptions enter `status=paused`, not when [payment collection](https://docs.stripe.com/billing/subscriptions/pause) is paused.
     */
    interface CustomerSubscriptionPausedEvent extends EventBase {
      type: 'customer.subscription.paused';
      data: CustomerSubscriptionPausedEvent.Data;
    }

    namespace CustomerSubscriptionPausedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Subscription;

        previous_attributes?: Partial<Stripe.Subscription>;
      }
    }

    /**
     * Occurs whenever a customer's subscription's pending update is applied, and the subscription is updated.
     */
    interface CustomerSubscriptionPendingUpdateAppliedEvent extends EventBase {
      type: 'customer.subscription.pending_update_applied';
      data: CustomerSubscriptionPendingUpdateAppliedEvent.Data;
    }

    namespace CustomerSubscriptionPendingUpdateAppliedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Subscription;

        previous_attributes?: Partial<Stripe.Subscription>;
      }
    }

    /**
     * Occurs whenever a customer's subscription's pending update expires before the related invoice is paid.
     */
    interface CustomerSubscriptionPendingUpdateExpiredEvent extends EventBase {
      type: 'customer.subscription.pending_update_expired';
      data: CustomerSubscriptionPendingUpdateExpiredEvent.Data;
    }

    namespace CustomerSubscriptionPendingUpdateExpiredEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Subscription;

        previous_attributes?: Partial<Stripe.Subscription>;
      }
    }

    /**
     * Occurs whenever a customer's subscription is no longer paused. Only applies when a `status=paused` subscription is [resumed](https://docs.stripe.com/api/subscriptions/resume), not when [payment collection](https://docs.stripe.com/billing/subscriptions/pause) is resumed.
     */
    interface CustomerSubscriptionResumedEvent extends EventBase {
      type: 'customer.subscription.resumed';
      data: CustomerSubscriptionResumedEvent.Data;
    }

    namespace CustomerSubscriptionResumedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Subscription;

        previous_attributes?: Partial<Stripe.Subscription>;
      }
    }

    /**
     * Occurs three days before a subscription's trial period is scheduled to end, or when a trial is ended immediately (using `trial_end=now`).
     */
    interface CustomerSubscriptionTrialWillEndEvent extends EventBase {
      type: 'customer.subscription.trial_will_end';
      data: CustomerSubscriptionTrialWillEndEvent.Data;
    }

    namespace CustomerSubscriptionTrialWillEndEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Subscription;

        previous_attributes?: Partial<Stripe.Subscription>;
      }
    }

    /**
     * Occurs whenever a subscription changes (e.g., switching from one plan to another, or changing the status from trial to active).
     */
    interface CustomerSubscriptionUpdatedEvent extends EventBase {
      type: 'customer.subscription.updated';
      data: CustomerSubscriptionUpdatedEvent.Data;
    }

    namespace CustomerSubscriptionUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Subscription;

        previous_attributes?: Partial<Stripe.Subscription>;
      }
    }

    /**
     * Occurs whenever a tax ID is created for a customer.
     */
    interface CustomerTaxIdCreatedEvent extends EventBase {
      type: 'customer.tax_id.created';
      data: CustomerTaxIdCreatedEvent.Data;
    }

    namespace CustomerTaxIdCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.TaxId;

        previous_attributes?: Partial<Stripe.TaxId>;
      }
    }

    /**
     * Occurs whenever a tax ID is deleted from a customer.
     */
    interface CustomerTaxIdDeletedEvent extends EventBase {
      type: 'customer.tax_id.deleted';
      data: CustomerTaxIdDeletedEvent.Data;
    }

    namespace CustomerTaxIdDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.TaxId;

        previous_attributes?: Partial<Stripe.TaxId>;
      }
    }

    /**
     * Occurs whenever a customer's tax ID is updated.
     */
    interface CustomerTaxIdUpdatedEvent extends EventBase {
      type: 'customer.tax_id.updated';
      data: CustomerTaxIdUpdatedEvent.Data;
    }

    namespace CustomerTaxIdUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.TaxId;

        previous_attributes?: Partial<Stripe.TaxId>;
      }
    }

    /**
     * Occurs whenever any property of a customer changes.
     */
    interface CustomerUpdatedEvent extends EventBase {
      type: 'customer.updated';
      data: CustomerUpdatedEvent.Data;
    }

    namespace CustomerUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Customer;

        previous_attributes?: Partial<Stripe.Customer>;
      }
    }

    /**
     * Occurs whenever a new customer cash balance transactions is created.
     */
    interface CustomerCashBalanceTransactionCreatedEvent extends EventBase {
      type: 'customer_cash_balance_transaction.created';
      data: CustomerCashBalanceTransactionCreatedEvent.Data;
    }

    namespace CustomerCashBalanceTransactionCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.CustomerCashBalanceTransaction;

        previous_attributes?: Partial<Stripe.CustomerCashBalanceTransaction>;
      }
    }

    /**
     * Occurs whenever a customer's entitlements change.
     */
    interface EntitlementsActiveEntitlementSummaryUpdatedEvent
      extends EventBase {
      type: 'entitlements.active_entitlement_summary.updated';
      data: EntitlementsActiveEntitlementSummaryUpdatedEvent.Data;
    }

    namespace EntitlementsActiveEntitlementSummaryUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Entitlements.ActiveEntitlementSummary;

        previous_attributes?: Partial<
          Stripe.Entitlements.ActiveEntitlementSummary
        >;
      }
    }

    /**
     * Occurs whenever a new Stripe-generated file is available for your account.
     */
    interface FileCreatedEvent extends EventBase {
      type: 'file.created';
      data: FileCreatedEvent.Data;
    }

    namespace FileCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.File;

        previous_attributes?: Partial<Stripe.File>;
      }
    }

    /**
     * Occurs when a new Financial Connections account is created.
     */
    interface FinancialConnectionsAccountCreatedEvent extends EventBase {
      type: 'financial_connections.account.created';
      data: FinancialConnectionsAccountCreatedEvent.Data;
    }

    namespace FinancialConnectionsAccountCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.FinancialConnections.Account;

        previous_attributes?: Partial<Stripe.FinancialConnections.Account>;
      }
    }

    /**
     * Occurs when a Financial Connections account's status is updated from `active` to `inactive`.
     */
    interface FinancialConnectionsAccountDeactivatedEvent extends EventBase {
      type: 'financial_connections.account.deactivated';
      data: FinancialConnectionsAccountDeactivatedEvent.Data;
    }

    namespace FinancialConnectionsAccountDeactivatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.FinancialConnections.Account;

        previous_attributes?: Partial<Stripe.FinancialConnections.Account>;
      }
    }

    /**
     * Occurs when a Financial Connections account is disconnected.
     */
    interface FinancialConnectionsAccountDisconnectedEvent extends EventBase {
      type: 'financial_connections.account.disconnected';
      data: FinancialConnectionsAccountDisconnectedEvent.Data;
    }

    namespace FinancialConnectionsAccountDisconnectedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.FinancialConnections.Account;

        previous_attributes?: Partial<Stripe.FinancialConnections.Account>;
      }
    }

    /**
     * Occurs when a Financial Connections account's status is updated from `inactive` to `active`.
     */
    interface FinancialConnectionsAccountReactivatedEvent extends EventBase {
      type: 'financial_connections.account.reactivated';
      data: FinancialConnectionsAccountReactivatedEvent.Data;
    }

    namespace FinancialConnectionsAccountReactivatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.FinancialConnections.Account;

        previous_attributes?: Partial<Stripe.FinancialConnections.Account>;
      }
    }

    /**
     * Occurs when an Account’s `balance_refresh` status transitions from `pending` to either `succeeded` or `failed`.
     */
    interface FinancialConnectionsAccountRefreshedBalanceEvent
      extends EventBase {
      type: 'financial_connections.account.refreshed_balance';
      data: FinancialConnectionsAccountRefreshedBalanceEvent.Data;
    }

    namespace FinancialConnectionsAccountRefreshedBalanceEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.FinancialConnections.Account;

        previous_attributes?: Partial<Stripe.FinancialConnections.Account>;
      }
    }

    /**
     * Occurs when an Account’s `ownership_refresh` status transitions from `pending` to either `succeeded` or `failed`.
     */
    interface FinancialConnectionsAccountRefreshedOwnershipEvent
      extends EventBase {
      type: 'financial_connections.account.refreshed_ownership';
      data: FinancialConnectionsAccountRefreshedOwnershipEvent.Data;
    }

    namespace FinancialConnectionsAccountRefreshedOwnershipEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.FinancialConnections.Account;

        previous_attributes?: Partial<Stripe.FinancialConnections.Account>;
      }
    }

    /**
     * Occurs when an Account’s `transaction_refresh` status transitions from `pending` to either `succeeded` or `failed`.
     */
    interface FinancialConnectionsAccountRefreshedTransactionsEvent
      extends EventBase {
      type: 'financial_connections.account.refreshed_transactions';
      data: FinancialConnectionsAccountRefreshedTransactionsEvent.Data;
    }

    namespace FinancialConnectionsAccountRefreshedTransactionsEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.FinancialConnections.Account;

        previous_attributes?: Partial<Stripe.FinancialConnections.Account>;
      }
    }

    /**
     * Occurs whenever a VerificationSession is canceled
     */
    interface IdentityVerificationSessionCanceledEvent extends EventBase {
      type: 'identity.verification_session.canceled';
      data: IdentityVerificationSessionCanceledEvent.Data;
    }

    namespace IdentityVerificationSessionCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Identity.VerificationSession;

        previous_attributes?: Partial<Stripe.Identity.VerificationSession>;
      }
    }

    /**
     * Occurs whenever a VerificationSession is created
     */
    interface IdentityVerificationSessionCreatedEvent extends EventBase {
      type: 'identity.verification_session.created';
      data: IdentityVerificationSessionCreatedEvent.Data;
    }

    namespace IdentityVerificationSessionCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Identity.VerificationSession;

        previous_attributes?: Partial<Stripe.Identity.VerificationSession>;
      }
    }

    /**
     * Occurs whenever a VerificationSession transitions to processing
     */
    interface IdentityVerificationSessionProcessingEvent extends EventBase {
      type: 'identity.verification_session.processing';
      data: IdentityVerificationSessionProcessingEvent.Data;
    }

    namespace IdentityVerificationSessionProcessingEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Identity.VerificationSession;

        previous_attributes?: Partial<Stripe.Identity.VerificationSession>;
      }
    }

    /**
     * Occurs whenever a VerificationSession is redacted.
     */
    interface IdentityVerificationSessionRedactedEvent extends EventBase {
      type: 'identity.verification_session.redacted';
      data: IdentityVerificationSessionRedactedEvent.Data;
    }

    namespace IdentityVerificationSessionRedactedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Identity.VerificationSession;

        previous_attributes?: Partial<Stripe.Identity.VerificationSession>;
      }
    }

    /**
     * Occurs whenever a VerificationSession transitions to require user input
     */
    interface IdentityVerificationSessionRequiresInputEvent extends EventBase {
      type: 'identity.verification_session.requires_input';
      data: IdentityVerificationSessionRequiresInputEvent.Data;
    }

    namespace IdentityVerificationSessionRequiresInputEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Identity.VerificationSession;

        previous_attributes?: Partial<Stripe.Identity.VerificationSession>;
      }
    }

    /**
     * Occurs whenever a VerificationSession transitions to verified
     */
    interface IdentityVerificationSessionVerifiedEvent extends EventBase {
      type: 'identity.verification_session.verified';
      data: IdentityVerificationSessionVerifiedEvent.Data;
    }

    namespace IdentityVerificationSessionVerifiedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Identity.VerificationSession;

        previous_attributes?: Partial<Stripe.Identity.VerificationSession>;
      }
    }

    /**
     * Occurs whenever a new invoice is created. To learn how webhooks can be used with this event, and how they can affect it, see [Using Webhooks with Subscriptions](https://docs.stripe.com/subscriptions/webhooks).
     */
    interface InvoiceCreatedEvent extends EventBase {
      type: 'invoice.created';
      data: InvoiceCreatedEvent.Data;
    }

    namespace InvoiceCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs whenever a draft invoice is deleted. Note: This event is not sent for [invoice previews](https://docs.stripe.com/api/invoices/create_preview).
     */
    interface InvoiceDeletedEvent extends EventBase {
      type: 'invoice.deleted';
      data: InvoiceDeletedEvent.Data;
    }

    namespace InvoiceDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs whenever a draft invoice cannot be finalized. See the invoice’s [last finalization error](https://docs.stripe.com/api/invoices/object#invoice_object-last_finalization_error) for details.
     */
    interface InvoiceFinalizationFailedEvent extends EventBase {
      type: 'invoice.finalization_failed';
      data: InvoiceFinalizationFailedEvent.Data;
    }

    namespace InvoiceFinalizationFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs whenever a draft invoice is finalized and updated to be an open invoice.
     */
    interface InvoiceFinalizedEvent extends EventBase {
      type: 'invoice.finalized';
      data: InvoiceFinalizedEvent.Data;
    }

    namespace InvoiceFinalizedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs whenever an invoice is marked uncollectible.
     */
    interface InvoiceMarkedUncollectibleEvent extends EventBase {
      type: 'invoice.marked_uncollectible';
      data: InvoiceMarkedUncollectibleEvent.Data;
    }

    namespace InvoiceMarkedUncollectibleEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs X number of days after an invoice becomes due&mdash;where X is determined by Automations
     */
    interface InvoiceOverdueEvent extends EventBase {
      type: 'invoice.overdue';
      data: InvoiceOverdueEvent.Data;
    }

    namespace InvoiceOverdueEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs when an invoice transitions to paid with a non-zero amount_overpaid.
     */
    interface InvoiceOverpaidEvent extends EventBase {
      type: 'invoice.overpaid';
      data: InvoiceOverpaidEvent.Data;
    }

    namespace InvoiceOverpaidEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs whenever an invoice payment attempt succeeds or an invoice is marked as paid out-of-band.
     */
    interface InvoicePaidEvent extends EventBase {
      type: 'invoice.paid';
      data: InvoicePaidEvent.Data;
    }

    namespace InvoicePaidEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs whenever an invoice payment attempt requires further user action to complete.
     */
    interface InvoicePaymentActionRequiredEvent extends EventBase {
      type: 'invoice.payment_action_required';
      data: InvoicePaymentActionRequiredEvent.Data;
    }

    namespace InvoicePaymentActionRequiredEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs whenever an invoice payment attempt fails, due to either a declined payment, including soft decline, or to the lack of a stored payment method.
     */
    interface InvoicePaymentFailedEvent extends EventBase {
      type: 'invoice.payment_failed';
      data: InvoicePaymentFailedEvent.Data;
    }

    namespace InvoicePaymentFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs whenever an invoice payment attempt succeeds.
     */
    interface InvoicePaymentSucceededEvent extends EventBase {
      type: 'invoice.payment_succeeded';
      data: InvoicePaymentSucceededEvent.Data;
    }

    namespace InvoicePaymentSucceededEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs whenever an invoice email is sent out.
     */
    interface InvoiceSentEvent extends EventBase {
      type: 'invoice.sent';
      data: InvoiceSentEvent.Data;
    }

    namespace InvoiceSentEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs X number of days before a subscription is scheduled to create an invoice that is automatically charged&mdash;where X is determined by your [subscriptions settings](https://dashboard.stripe.com/account/billing/automatic). Note: The received `Invoice` object will not have an invoice ID.
     */
    interface InvoiceUpcomingEvent extends EventBase {
      type: 'invoice.upcoming';
      data: InvoiceUpcomingEvent.Data;
    }

    namespace InvoiceUpcomingEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs whenever an invoice changes (e.g., the invoice amount).
     */
    interface InvoiceUpdatedEvent extends EventBase {
      type: 'invoice.updated';
      data: InvoiceUpdatedEvent.Data;
    }

    namespace InvoiceUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs whenever an invoice is voided.
     */
    interface InvoiceVoidedEvent extends EventBase {
      type: 'invoice.voided';
      data: InvoiceVoidedEvent.Data;
    }

    namespace InvoiceVoidedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs X number of days before an invoice becomes due&mdash;where X is determined by Automations
     */
    interface InvoiceWillBeDueEvent extends EventBase {
      type: 'invoice.will_be_due';
      data: InvoiceWillBeDueEvent.Data;
    }

    namespace InvoiceWillBeDueEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Invoice;

        previous_attributes?: Partial<Stripe.Invoice>;
      }
    }

    /**
     * Occurs when an InvoicePayment is successfully paid.
     */
    interface InvoicePaymentPaidEvent extends EventBase {
      type: 'invoice_payment.paid';
      data: InvoicePaymentPaidEvent.Data;
    }

    namespace InvoicePaymentPaidEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.InvoicePayment;

        previous_attributes?: Partial<Stripe.InvoicePayment>;
      }
    }

    /**
     * Occurs whenever an invoice item is created.
     */
    interface InvoiceItemCreatedEvent extends EventBase {
      type: 'invoiceitem.created';
      data: InvoiceItemCreatedEvent.Data;
    }

    namespace InvoiceItemCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.InvoiceItem;

        previous_attributes?: Partial<Stripe.InvoiceItem>;
      }
    }

    /**
     * Occurs whenever an invoice item is deleted.
     */
    interface InvoiceItemDeletedEvent extends EventBase {
      type: 'invoiceitem.deleted';
      data: InvoiceItemDeletedEvent.Data;
    }

    namespace InvoiceItemDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.InvoiceItem;

        previous_attributes?: Partial<Stripe.InvoiceItem>;
      }
    }

    /**
     * Occurs whenever an authorization is created.
     */
    interface IssuingAuthorizationCreatedEvent extends EventBase {
      type: 'issuing_authorization.created';
      data: IssuingAuthorizationCreatedEvent.Data;
    }

    namespace IssuingAuthorizationCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Authorization;

        previous_attributes?: Partial<Stripe.Issuing.Authorization>;
      }
    }

    /**
     * Represents a synchronous request for authorization, see [Using your integration to handle authorization requests](https://docs.stripe.com/issuing/purchases/authorizations#authorization-handling).
     */
    interface IssuingAuthorizationRequestEvent extends EventBase {
      type: 'issuing_authorization.request';
      data: IssuingAuthorizationRequestEvent.Data;
    }

    namespace IssuingAuthorizationRequestEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Authorization;

        previous_attributes?: Partial<Stripe.Issuing.Authorization>;
      }
    }

    /**
     * Occurs whenever an authorization is updated.
     */
    interface IssuingAuthorizationUpdatedEvent extends EventBase {
      type: 'issuing_authorization.updated';
      data: IssuingAuthorizationUpdatedEvent.Data;
    }

    namespace IssuingAuthorizationUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Authorization;

        previous_attributes?: Partial<Stripe.Issuing.Authorization>;
      }
    }

    /**
     * Occurs whenever a card is created.
     */
    interface IssuingCardCreatedEvent extends EventBase {
      type: 'issuing_card.created';
      data: IssuingCardCreatedEvent.Data;
    }

    namespace IssuingCardCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Card;

        previous_attributes?: Partial<Stripe.Issuing.Card>;
      }
    }

    /**
     * Occurs whenever a card is updated.
     */
    interface IssuingCardUpdatedEvent extends EventBase {
      type: 'issuing_card.updated';
      data: IssuingCardUpdatedEvent.Data;
    }

    namespace IssuingCardUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Card;

        previous_attributes?: Partial<Stripe.Issuing.Card>;
      }
    }

    /**
     * Occurs whenever a cardholder is created.
     */
    interface IssuingCardholderCreatedEvent extends EventBase {
      type: 'issuing_cardholder.created';
      data: IssuingCardholderCreatedEvent.Data;
    }

    namespace IssuingCardholderCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Cardholder;

        previous_attributes?: Partial<Stripe.Issuing.Cardholder>;
      }
    }

    /**
     * Occurs whenever a cardholder is updated.
     */
    interface IssuingCardholderUpdatedEvent extends EventBase {
      type: 'issuing_cardholder.updated';
      data: IssuingCardholderUpdatedEvent.Data;
    }

    namespace IssuingCardholderUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Cardholder;

        previous_attributes?: Partial<Stripe.Issuing.Cardholder>;
      }
    }

    /**
     * Occurs whenever a dispute is won, lost or expired.
     */
    interface IssuingDisputeClosedEvent extends EventBase {
      type: 'issuing_dispute.closed';
      data: IssuingDisputeClosedEvent.Data;
    }

    namespace IssuingDisputeClosedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Dispute;

        previous_attributes?: Partial<Stripe.Issuing.Dispute>;
      }
    }

    /**
     * Occurs whenever a dispute is created.
     */
    interface IssuingDisputeCreatedEvent extends EventBase {
      type: 'issuing_dispute.created';
      data: IssuingDisputeCreatedEvent.Data;
    }

    namespace IssuingDisputeCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Dispute;

        previous_attributes?: Partial<Stripe.Issuing.Dispute>;
      }
    }

    /**
     * Occurs whenever funds are reinstated to your account for an Issuing dispute.
     */
    interface IssuingDisputeFundsReinstatedEvent extends EventBase {
      type: 'issuing_dispute.funds_reinstated';
      data: IssuingDisputeFundsReinstatedEvent.Data;
    }

    namespace IssuingDisputeFundsReinstatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Dispute;

        previous_attributes?: Partial<Stripe.Issuing.Dispute>;
      }
    }

    /**
     * Occurs whenever funds are deducted from your account for an Issuing dispute.
     */
    interface IssuingDisputeFundsRescindedEvent extends EventBase {
      type: 'issuing_dispute.funds_rescinded';
      data: IssuingDisputeFundsRescindedEvent.Data;
    }

    namespace IssuingDisputeFundsRescindedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Dispute;

        previous_attributes?: Partial<Stripe.Issuing.Dispute>;
      }
    }

    /**
     * Occurs whenever a dispute is submitted.
     */
    interface IssuingDisputeSubmittedEvent extends EventBase {
      type: 'issuing_dispute.submitted';
      data: IssuingDisputeSubmittedEvent.Data;
    }

    namespace IssuingDisputeSubmittedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Dispute;

        previous_attributes?: Partial<Stripe.Issuing.Dispute>;
      }
    }

    /**
     * Occurs whenever a dispute is updated.
     */
    interface IssuingDisputeUpdatedEvent extends EventBase {
      type: 'issuing_dispute.updated';
      data: IssuingDisputeUpdatedEvent.Data;
    }

    namespace IssuingDisputeUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Dispute;

        previous_attributes?: Partial<Stripe.Issuing.Dispute>;
      }
    }

    /**
     * Occurs whenever a personalization design is activated following the activation of the physical bundle that belongs to it.
     */
    interface IssuingPersonalizationDesignActivatedEvent extends EventBase {
      type: 'issuing_personalization_design.activated';
      data: IssuingPersonalizationDesignActivatedEvent.Data;
    }

    namespace IssuingPersonalizationDesignActivatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.PersonalizationDesign;

        previous_attributes?: Partial<Stripe.Issuing.PersonalizationDesign>;
      }
    }

    /**
     * Occurs whenever a personalization design is deactivated following the deactivation of the physical bundle that belongs to it.
     */
    interface IssuingPersonalizationDesignDeactivatedEvent extends EventBase {
      type: 'issuing_personalization_design.deactivated';
      data: IssuingPersonalizationDesignDeactivatedEvent.Data;
    }

    namespace IssuingPersonalizationDesignDeactivatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.PersonalizationDesign;

        previous_attributes?: Partial<Stripe.Issuing.PersonalizationDesign>;
      }
    }

    /**
     * Occurs whenever a personalization design is rejected by design review.
     */
    interface IssuingPersonalizationDesignRejectedEvent extends EventBase {
      type: 'issuing_personalization_design.rejected';
      data: IssuingPersonalizationDesignRejectedEvent.Data;
    }

    namespace IssuingPersonalizationDesignRejectedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.PersonalizationDesign;

        previous_attributes?: Partial<Stripe.Issuing.PersonalizationDesign>;
      }
    }

    /**
     * Occurs whenever a personalization design is updated.
     */
    interface IssuingPersonalizationDesignUpdatedEvent extends EventBase {
      type: 'issuing_personalization_design.updated';
      data: IssuingPersonalizationDesignUpdatedEvent.Data;
    }

    namespace IssuingPersonalizationDesignUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.PersonalizationDesign;

        previous_attributes?: Partial<Stripe.Issuing.PersonalizationDesign>;
      }
    }

    /**
     * Occurs whenever an issuing digital wallet token is created.
     */
    interface IssuingTokenCreatedEvent extends EventBase {
      type: 'issuing_token.created';
      data: IssuingTokenCreatedEvent.Data;
    }

    namespace IssuingTokenCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Token;

        previous_attributes?: Partial<Stripe.Issuing.Token>;
      }
    }

    /**
     * Occurs whenever an issuing digital wallet token is updated.
     */
    interface IssuingTokenUpdatedEvent extends EventBase {
      type: 'issuing_token.updated';
      data: IssuingTokenUpdatedEvent.Data;
    }

    namespace IssuingTokenUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Token;

        previous_attributes?: Partial<Stripe.Issuing.Token>;
      }
    }

    /**
     * Occurs whenever an issuing transaction is created.
     */
    interface IssuingTransactionCreatedEvent extends EventBase {
      type: 'issuing_transaction.created';
      data: IssuingTransactionCreatedEvent.Data;
    }

    namespace IssuingTransactionCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Transaction;

        previous_attributes?: Partial<Stripe.Issuing.Transaction>;
      }
    }

    /**
     * Occurs whenever an issuing transaction is updated with receipt data.
     */
    interface IssuingTransactionPurchaseDetailsReceiptUpdatedEvent
      extends EventBase {
      type: 'issuing_transaction.purchase_details_receipt_updated';
      data: IssuingTransactionPurchaseDetailsReceiptUpdatedEvent.Data;
    }

    namespace IssuingTransactionPurchaseDetailsReceiptUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Transaction;

        previous_attributes?: Partial<Stripe.Issuing.Transaction>;
      }
    }

    /**
     * Occurs whenever an issuing transaction is updated.
     */
    interface IssuingTransactionUpdatedEvent extends EventBase {
      type: 'issuing_transaction.updated';
      data: IssuingTransactionUpdatedEvent.Data;
    }

    namespace IssuingTransactionUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Issuing.Transaction;

        previous_attributes?: Partial<Stripe.Issuing.Transaction>;
      }
    }

    /**
     * Occurs whenever a Mandate is updated.
     */
    interface MandateUpdatedEvent extends EventBase {
      type: 'mandate.updated';
      data: MandateUpdatedEvent.Data;
    }

    namespace MandateUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Mandate;

        previous_attributes?: Partial<Stripe.Mandate>;
      }
    }

    /**
     * Occurs when a PaymentIntent has funds to be captured. Check the `amount_capturable` property on the PaymentIntent to determine the amount that can be captured. You may capture the PaymentIntent with an `amount_to_capture` value up to the specified amount. [Learn more about capturing PaymentIntents.](https://docs.stripe.com/api/payment_intents/capture)
     */
    interface PaymentIntentAmountCapturableUpdatedEvent extends EventBase {
      type: 'payment_intent.amount_capturable_updated';
      data: PaymentIntentAmountCapturableUpdatedEvent.Data;
    }

    namespace PaymentIntentAmountCapturableUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentIntent;

        previous_attributes?: Partial<Stripe.PaymentIntent>;
      }
    }

    /**
     * Occurs when a PaymentIntent is canceled.
     */
    interface PaymentIntentCanceledEvent extends EventBase {
      type: 'payment_intent.canceled';
      data: PaymentIntentCanceledEvent.Data;
    }

    namespace PaymentIntentCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentIntent;

        previous_attributes?: Partial<Stripe.PaymentIntent>;
      }
    }

    /**
     * Occurs when a new PaymentIntent is created.
     */
    interface PaymentIntentCreatedEvent extends EventBase {
      type: 'payment_intent.created';
      data: PaymentIntentCreatedEvent.Data;
    }

    namespace PaymentIntentCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentIntent;

        previous_attributes?: Partial<Stripe.PaymentIntent>;
      }
    }

    /**
     * Occurs when funds are applied to a customer_balance PaymentIntent and the 'amount_remaining' changes.
     */
    interface PaymentIntentPartiallyFundedEvent extends EventBase {
      type: 'payment_intent.partially_funded';
      data: PaymentIntentPartiallyFundedEvent.Data;
    }

    namespace PaymentIntentPartiallyFundedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentIntent;

        previous_attributes?: Partial<Stripe.PaymentIntent>;
      }
    }

    /**
     * Occurs when a PaymentIntent has failed the attempt to create a payment method or a payment.
     */
    interface PaymentIntentPaymentFailedEvent extends EventBase {
      type: 'payment_intent.payment_failed';
      data: PaymentIntentPaymentFailedEvent.Data;
    }

    namespace PaymentIntentPaymentFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentIntent;

        previous_attributes?: Partial<Stripe.PaymentIntent>;
      }
    }

    /**
     * Occurs when a PaymentIntent has started processing.
     */
    interface PaymentIntentProcessingEvent extends EventBase {
      type: 'payment_intent.processing';
      data: PaymentIntentProcessingEvent.Data;
    }

    namespace PaymentIntentProcessingEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentIntent;

        previous_attributes?: Partial<Stripe.PaymentIntent>;
      }
    }

    /**
     * Occurs when a PaymentIntent transitions to requires_action state
     */
    interface PaymentIntentRequiresActionEvent extends EventBase {
      type: 'payment_intent.requires_action';
      data: PaymentIntentRequiresActionEvent.Data;
    }

    namespace PaymentIntentRequiresActionEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentIntent;

        previous_attributes?: Partial<Stripe.PaymentIntent>;
      }
    }

    /**
     * Occurs when a PaymentIntent has successfully completed payment.
     */
    interface PaymentIntentSucceededEvent extends EventBase {
      type: 'payment_intent.succeeded';
      data: PaymentIntentSucceededEvent.Data;
    }

    namespace PaymentIntentSucceededEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentIntent;

        previous_attributes?: Partial<Stripe.PaymentIntent>;
      }
    }

    /**
     * Occurs when a payment link is created.
     */
    interface PaymentLinkCreatedEvent extends EventBase {
      type: 'payment_link.created';
      data: PaymentLinkCreatedEvent.Data;
    }

    namespace PaymentLinkCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentLink;

        previous_attributes?: Partial<Stripe.PaymentLink>;
      }
    }

    /**
     * Occurs when a payment link is updated.
     */
    interface PaymentLinkUpdatedEvent extends EventBase {
      type: 'payment_link.updated';
      data: PaymentLinkUpdatedEvent.Data;
    }

    namespace PaymentLinkUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentLink;

        previous_attributes?: Partial<Stripe.PaymentLink>;
      }
    }

    /**
     * Occurs whenever a new payment method is attached to a customer.
     */
    interface PaymentMethodAttachedEvent extends EventBase {
      type: 'payment_method.attached';
      data: PaymentMethodAttachedEvent.Data;
    }

    namespace PaymentMethodAttachedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentMethod;

        previous_attributes?: Partial<Stripe.PaymentMethod>;
      }
    }

    /**
     * Occurs whenever a payment method's details are automatically updated by the network.
     */
    interface PaymentMethodAutomaticallyUpdatedEvent extends EventBase {
      type: 'payment_method.automatically_updated';
      data: PaymentMethodAutomaticallyUpdatedEvent.Data;
    }

    namespace PaymentMethodAutomaticallyUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentMethod;

        previous_attributes?: Partial<Stripe.PaymentMethod>;
      }
    }

    /**
     * Occurs whenever a payment method is detached from a customer.
     */
    interface PaymentMethodDetachedEvent extends EventBase {
      type: 'payment_method.detached';
      data: PaymentMethodDetachedEvent.Data;
    }

    namespace PaymentMethodDetachedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentMethod;

        previous_attributes?: Partial<Stripe.PaymentMethod>;
      }
    }

    /**
     * Occurs whenever a payment method is updated via the [PaymentMethod update API](https://docs.stripe.com/api/payment_methods/update).
     */
    interface PaymentMethodUpdatedEvent extends EventBase {
      type: 'payment_method.updated';
      data: PaymentMethodUpdatedEvent.Data;
    }

    namespace PaymentMethodUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PaymentMethod;

        previous_attributes?: Partial<Stripe.PaymentMethod>;
      }
    }

    /**
     * Occurs whenever a payout is canceled.
     */
    interface PayoutCanceledEvent extends EventBase {
      type: 'payout.canceled';
      data: PayoutCanceledEvent.Data;
    }

    namespace PayoutCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Payout;

        previous_attributes?: Partial<Stripe.Payout>;
      }
    }

    /**
     * Occurs whenever a payout is created.
     */
    interface PayoutCreatedEvent extends EventBase {
      type: 'payout.created';
      data: PayoutCreatedEvent.Data;
    }

    namespace PayoutCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Payout;

        previous_attributes?: Partial<Stripe.Payout>;
      }
    }

    /**
     * Occurs whenever a payout attempt fails.
     */
    interface PayoutFailedEvent extends EventBase {
      type: 'payout.failed';
      data: PayoutFailedEvent.Data;
    }

    namespace PayoutFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Payout;

        previous_attributes?: Partial<Stripe.Payout>;
      }
    }

    /**
     * Occurs whenever a payout is *expected* to be available in the destination account. If the payout fails, a `payout.failed` notification is also sent, at a later time.
     */
    interface PayoutPaidEvent extends EventBase {
      type: 'payout.paid';
      data: PayoutPaidEvent.Data;
    }

    namespace PayoutPaidEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Payout;

        previous_attributes?: Partial<Stripe.Payout>;
      }
    }

    /**
     * Occurs whenever balance transactions paid out in an automatic payout can be queried.
     */
    interface PayoutReconciliationCompletedEvent extends EventBase {
      type: 'payout.reconciliation_completed';
      data: PayoutReconciliationCompletedEvent.Data;
    }

    namespace PayoutReconciliationCompletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Payout;

        previous_attributes?: Partial<Stripe.Payout>;
      }
    }

    /**
     * Occurs whenever a payout is updated.
     */
    interface PayoutUpdatedEvent extends EventBase {
      type: 'payout.updated';
      data: PayoutUpdatedEvent.Data;
    }

    namespace PayoutUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Payout;

        previous_attributes?: Partial<Stripe.Payout>;
      }
    }

    /**
     * Occurs whenever a person associated with an account is created.
     */
    interface PersonCreatedEvent extends EventBase {
      type: 'person.created';
      data: PersonCreatedEvent.Data;
    }

    namespace PersonCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Person;

        previous_attributes?: Partial<Stripe.Person>;
      }
    }

    /**
     * Occurs whenever a person associated with an account is deleted.
     */
    interface PersonDeletedEvent extends EventBase {
      type: 'person.deleted';
      data: PersonDeletedEvent.Data;
    }

    namespace PersonDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Person;

        previous_attributes?: Partial<Stripe.Person>;
      }
    }

    /**
     * Occurs whenever a person associated with an account is updated.
     */
    interface PersonUpdatedEvent extends EventBase {
      type: 'person.updated';
      data: PersonUpdatedEvent.Data;
    }

    namespace PersonUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Person;

        previous_attributes?: Partial<Stripe.Person>;
      }
    }

    /**
     * Occurs whenever a plan is created.
     */
    interface PlanCreatedEvent extends EventBase {
      type: 'plan.created';
      data: PlanCreatedEvent.Data;
    }

    namespace PlanCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Plan;

        previous_attributes?: Partial<Stripe.Plan>;
      }
    }

    /**
     * Occurs whenever a plan is deleted.
     */
    interface PlanDeletedEvent extends EventBase {
      type: 'plan.deleted';
      data: PlanDeletedEvent.Data;
    }

    namespace PlanDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Plan;

        previous_attributes?: Partial<Stripe.Plan>;
      }
    }

    /**
     * Occurs whenever a plan is updated.
     */
    interface PlanUpdatedEvent extends EventBase {
      type: 'plan.updated';
      data: PlanUpdatedEvent.Data;
    }

    namespace PlanUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Plan;

        previous_attributes?: Partial<Stripe.Plan>;
      }
    }

    /**
     * Occurs whenever a price is created.
     */
    interface PriceCreatedEvent extends EventBase {
      type: 'price.created';
      data: PriceCreatedEvent.Data;
    }

    namespace PriceCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Price;

        previous_attributes?: Partial<Stripe.Price>;
      }
    }

    /**
     * Occurs whenever a price is deleted.
     */
    interface PriceDeletedEvent extends EventBase {
      type: 'price.deleted';
      data: PriceDeletedEvent.Data;
    }

    namespace PriceDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Price;

        previous_attributes?: Partial<Stripe.Price>;
      }
    }

    /**
     * Occurs whenever a price is updated.
     */
    interface PriceUpdatedEvent extends EventBase {
      type: 'price.updated';
      data: PriceUpdatedEvent.Data;
    }

    namespace PriceUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Price;

        previous_attributes?: Partial<Stripe.Price>;
      }
    }

    /**
     * Occurs whenever a product is created.
     */
    interface ProductCreatedEvent extends EventBase {
      type: 'product.created';
      data: ProductCreatedEvent.Data;
    }

    namespace ProductCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Product;

        previous_attributes?: Partial<Stripe.Product>;
      }
    }

    /**
     * Occurs whenever a product is deleted.
     */
    interface ProductDeletedEvent extends EventBase {
      type: 'product.deleted';
      data: ProductDeletedEvent.Data;
    }

    namespace ProductDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Product;

        previous_attributes?: Partial<Stripe.Product>;
      }
    }

    /**
     * Occurs whenever a product is updated.
     */
    interface ProductUpdatedEvent extends EventBase {
      type: 'product.updated';
      data: ProductUpdatedEvent.Data;
    }

    namespace ProductUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Product;

        previous_attributes?: Partial<Stripe.Product>;
      }
    }

    /**
     * Occurs whenever a promotion code is created.
     */
    interface PromotionCodeCreatedEvent extends EventBase {
      type: 'promotion_code.created';
      data: PromotionCodeCreatedEvent.Data;
    }

    namespace PromotionCodeCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PromotionCode;

        previous_attributes?: Partial<Stripe.PromotionCode>;
      }
    }

    /**
     * Occurs whenever a promotion code is updated.
     */
    interface PromotionCodeUpdatedEvent extends EventBase {
      type: 'promotion_code.updated';
      data: PromotionCodeUpdatedEvent.Data;
    }

    namespace PromotionCodeUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.PromotionCode;

        previous_attributes?: Partial<Stripe.PromotionCode>;
      }
    }

    /**
     * Occurs whenever a quote is accepted.
     */
    interface QuoteAcceptedEvent extends EventBase {
      type: 'quote.accepted';
      data: QuoteAcceptedEvent.Data;
    }

    namespace QuoteAcceptedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Quote;

        previous_attributes?: Partial<Stripe.Quote>;
      }
    }

    /**
     * Occurs whenever a quote is canceled.
     */
    interface QuoteCanceledEvent extends EventBase {
      type: 'quote.canceled';
      data: QuoteCanceledEvent.Data;
    }

    namespace QuoteCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Quote;

        previous_attributes?: Partial<Stripe.Quote>;
      }
    }

    /**
     * Occurs whenever a quote is created.
     */
    interface QuoteCreatedEvent extends EventBase {
      type: 'quote.created';
      data: QuoteCreatedEvent.Data;
    }

    namespace QuoteCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Quote;

        previous_attributes?: Partial<Stripe.Quote>;
      }
    }

    /**
     * Occurs whenever a quote is finalized.
     */
    interface QuoteFinalizedEvent extends EventBase {
      type: 'quote.finalized';
      data: QuoteFinalizedEvent.Data;
    }

    namespace QuoteFinalizedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Quote;

        previous_attributes?: Partial<Stripe.Quote>;
      }
    }

    /**
     * Occurs whenever an early fraud warning is created.
     */
    interface RadarEarlyFraudWarningCreatedEvent extends EventBase {
      type: 'radar.early_fraud_warning.created';
      data: RadarEarlyFraudWarningCreatedEvent.Data;
    }

    namespace RadarEarlyFraudWarningCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Radar.EarlyFraudWarning;

        previous_attributes?: Partial<Stripe.Radar.EarlyFraudWarning>;
      }
    }

    /**
     * Occurs whenever an early fraud warning is updated.
     */
    interface RadarEarlyFraudWarningUpdatedEvent extends EventBase {
      type: 'radar.early_fraud_warning.updated';
      data: RadarEarlyFraudWarningUpdatedEvent.Data;
    }

    namespace RadarEarlyFraudWarningUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Radar.EarlyFraudWarning;

        previous_attributes?: Partial<Stripe.Radar.EarlyFraudWarning>;
      }
    }

    /**
     * Occurs whenever a refund is created.
     */
    interface RefundCreatedEvent extends EventBase {
      type: 'refund.created';
      data: RefundCreatedEvent.Data;
    }

    namespace RefundCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Refund;

        previous_attributes?: Partial<Stripe.Refund>;
      }
    }

    /**
     * Occurs whenever a refund has failed.
     */
    interface RefundFailedEvent extends EventBase {
      type: 'refund.failed';
      data: RefundFailedEvent.Data;
    }

    namespace RefundFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Refund;

        previous_attributes?: Partial<Stripe.Refund>;
      }
    }

    /**
     * Occurs whenever a refund is updated.
     */
    interface RefundUpdatedEvent extends EventBase {
      type: 'refund.updated';
      data: RefundUpdatedEvent.Data;
    }

    namespace RefundUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Refund;

        previous_attributes?: Partial<Stripe.Refund>;
      }
    }

    /**
     * Occurs whenever a requested `ReportRun` failed to complete.
     */
    interface ReportingReportRunFailedEvent extends EventBase {
      type: 'reporting.report_run.failed';
      data: ReportingReportRunFailedEvent.Data;
    }

    namespace ReportingReportRunFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Reporting.ReportRun;

        previous_attributes?: Partial<Stripe.Reporting.ReportRun>;
      }
    }

    /**
     * Occurs whenever a requested `ReportRun` completed successfully.
     */
    interface ReportingReportRunSucceededEvent extends EventBase {
      type: 'reporting.report_run.succeeded';
      data: ReportingReportRunSucceededEvent.Data;
    }

    namespace ReportingReportRunSucceededEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Reporting.ReportRun;

        previous_attributes?: Partial<Stripe.Reporting.ReportRun>;
      }
    }

    /**
     * Occurs whenever a `ReportType` is updated (typically to indicate that a new day's data has come available).
     */
    interface ReportingReportTypeUpdatedEvent extends EventBase {
      type: 'reporting.report_type.updated';
      data: ReportingReportTypeUpdatedEvent.Data;
    }

    namespace ReportingReportTypeUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Reporting.ReportType;

        previous_attributes?: Partial<Stripe.Reporting.ReportType>;
      }
    }

    /**
     * Occurs whenever a review is closed. The review's `reason` field indicates why: `approved`, `disputed`, `refunded`, `refunded_as_fraud`, or `canceled`.
     */
    interface ReviewClosedEvent extends EventBase {
      type: 'review.closed';
      data: ReviewClosedEvent.Data;
    }

    namespace ReviewClosedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Review;

        previous_attributes?: Partial<Stripe.Review>;
      }
    }

    /**
     * Occurs whenever a review is opened.
     */
    interface ReviewOpenedEvent extends EventBase {
      type: 'review.opened';
      data: ReviewOpenedEvent.Data;
    }

    namespace ReviewOpenedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Review;

        previous_attributes?: Partial<Stripe.Review>;
      }
    }

    /**
     * Occurs when a SetupIntent is canceled.
     */
    interface SetupIntentCanceledEvent extends EventBase {
      type: 'setup_intent.canceled';
      data: SetupIntentCanceledEvent.Data;
    }

    namespace SetupIntentCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SetupIntent;

        previous_attributes?: Partial<Stripe.SetupIntent>;
      }
    }

    /**
     * Occurs when a new SetupIntent is created.
     */
    interface SetupIntentCreatedEvent extends EventBase {
      type: 'setup_intent.created';
      data: SetupIntentCreatedEvent.Data;
    }

    namespace SetupIntentCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SetupIntent;

        previous_attributes?: Partial<Stripe.SetupIntent>;
      }
    }

    /**
     * Occurs when a SetupIntent is in requires_action state.
     */
    interface SetupIntentRequiresActionEvent extends EventBase {
      type: 'setup_intent.requires_action';
      data: SetupIntentRequiresActionEvent.Data;
    }

    namespace SetupIntentRequiresActionEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SetupIntent;

        previous_attributes?: Partial<Stripe.SetupIntent>;
      }
    }

    /**
     * Occurs when a SetupIntent has failed the attempt to setup a payment method.
     */
    interface SetupIntentSetupFailedEvent extends EventBase {
      type: 'setup_intent.setup_failed';
      data: SetupIntentSetupFailedEvent.Data;
    }

    namespace SetupIntentSetupFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SetupIntent;

        previous_attributes?: Partial<Stripe.SetupIntent>;
      }
    }

    /**
     * Occurs when an SetupIntent has successfully setup a payment method.
     */
    interface SetupIntentSucceededEvent extends EventBase {
      type: 'setup_intent.succeeded';
      data: SetupIntentSucceededEvent.Data;
    }

    namespace SetupIntentSucceededEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SetupIntent;

        previous_attributes?: Partial<Stripe.SetupIntent>;
      }
    }

    /**
     * Occurs whenever a Sigma scheduled query run finishes.
     */
    interface SigmaScheduledQueryRunCreatedEvent extends EventBase {
      type: 'sigma.scheduled_query_run.created';
      data: SigmaScheduledQueryRunCreatedEvent.Data;
    }

    namespace SigmaScheduledQueryRunCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Sigma.ScheduledQueryRun;

        previous_attributes?: Partial<Stripe.Sigma.ScheduledQueryRun>;
      }
    }

    /**
     * Occurs whenever a source is canceled.
     */
    interface SourceCanceledEvent extends EventBase {
      type: 'source.canceled';
      data: SourceCanceledEvent.Data;
    }

    namespace SourceCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Source;

        previous_attributes?: Partial<Stripe.Source>;
      }
    }

    /**
     * Occurs whenever a source transitions to chargeable.
     */
    interface SourceChargeableEvent extends EventBase {
      type: 'source.chargeable';
      data: SourceChargeableEvent.Data;
    }

    namespace SourceChargeableEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Source;

        previous_attributes?: Partial<Stripe.Source>;
      }
    }

    /**
     * Occurs whenever a source fails.
     */
    interface SourceFailedEvent extends EventBase {
      type: 'source.failed';
      data: SourceFailedEvent.Data;
    }

    namespace SourceFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Source;

        previous_attributes?: Partial<Stripe.Source>;
      }
    }

    /**
     * Occurs whenever a source mandate notification method is set to manual.
     */
    interface SourceMandateNotificationEvent extends EventBase {
      type: 'source.mandate_notification';
      data: SourceMandateNotificationEvent.Data;
    }

    namespace SourceMandateNotificationEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SourceMandateNotification;

        previous_attributes?: Partial<Stripe.SourceMandateNotification>;
      }
    }

    /**
     * Occurs whenever the refund attributes are required on a receiver source to process a refund or a mispayment.
     */
    interface SourceRefundAttributesRequiredEvent extends EventBase {
      type: 'source.refund_attributes_required';
      data: SourceRefundAttributesRequiredEvent.Data;
    }

    namespace SourceRefundAttributesRequiredEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Source;

        previous_attributes?: Partial<Stripe.Source>;
      }
    }

    /**
     * Occurs whenever a source transaction is created.
     */
    interface SourceTransactionCreatedEvent extends EventBase {
      type: 'source.transaction.created';
      data: SourceTransactionCreatedEvent.Data;
    }

    namespace SourceTransactionCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SourceTransaction;

        previous_attributes?: Partial<Stripe.SourceTransaction>;
      }
    }

    /**
     * Occurs whenever a source transaction is updated.
     */
    interface SourceTransactionUpdatedEvent extends EventBase {
      type: 'source.transaction.updated';
      data: SourceTransactionUpdatedEvent.Data;
    }

    namespace SourceTransactionUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SourceTransaction;

        previous_attributes?: Partial<Stripe.SourceTransaction>;
      }
    }

    /**
     * Occurs whenever a subscription schedule is canceled due to the underlying subscription being canceled because of delinquency.
     */
    interface SubscriptionScheduleAbortedEvent extends EventBase {
      type: 'subscription_schedule.aborted';
      data: SubscriptionScheduleAbortedEvent.Data;
    }

    namespace SubscriptionScheduleAbortedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SubscriptionSchedule;

        previous_attributes?: Partial<Stripe.SubscriptionSchedule>;
      }
    }

    /**
     * Occurs whenever a subscription schedule is canceled.
     */
    interface SubscriptionScheduleCanceledEvent extends EventBase {
      type: 'subscription_schedule.canceled';
      data: SubscriptionScheduleCanceledEvent.Data;
    }

    namespace SubscriptionScheduleCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SubscriptionSchedule;

        previous_attributes?: Partial<Stripe.SubscriptionSchedule>;
      }
    }

    /**
     * Occurs whenever a new subscription schedule is completed.
     */
    interface SubscriptionScheduleCompletedEvent extends EventBase {
      type: 'subscription_schedule.completed';
      data: SubscriptionScheduleCompletedEvent.Data;
    }

    namespace SubscriptionScheduleCompletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SubscriptionSchedule;

        previous_attributes?: Partial<Stripe.SubscriptionSchedule>;
      }
    }

    /**
     * Occurs whenever a new subscription schedule is created.
     */
    interface SubscriptionScheduleCreatedEvent extends EventBase {
      type: 'subscription_schedule.created';
      data: SubscriptionScheduleCreatedEvent.Data;
    }

    namespace SubscriptionScheduleCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SubscriptionSchedule;

        previous_attributes?: Partial<Stripe.SubscriptionSchedule>;
      }
    }

    /**
     * Occurs 7 days before a subscription schedule will expire.
     */
    interface SubscriptionScheduleExpiringEvent extends EventBase {
      type: 'subscription_schedule.expiring';
      data: SubscriptionScheduleExpiringEvent.Data;
    }

    namespace SubscriptionScheduleExpiringEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SubscriptionSchedule;

        previous_attributes?: Partial<Stripe.SubscriptionSchedule>;
      }
    }

    /**
     * Occurs whenever a new subscription schedule is released.
     */
    interface SubscriptionScheduleReleasedEvent extends EventBase {
      type: 'subscription_schedule.released';
      data: SubscriptionScheduleReleasedEvent.Data;
    }

    namespace SubscriptionScheduleReleasedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SubscriptionSchedule;

        previous_attributes?: Partial<Stripe.SubscriptionSchedule>;
      }
    }

    /**
     * Occurs whenever a subscription schedule is updated.
     */
    interface SubscriptionScheduleUpdatedEvent extends EventBase {
      type: 'subscription_schedule.updated';
      data: SubscriptionScheduleUpdatedEvent.Data;
    }

    namespace SubscriptionScheduleUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.SubscriptionSchedule;

        previous_attributes?: Partial<Stripe.SubscriptionSchedule>;
      }
    }

    /**
     * Occurs whenever tax settings is updated.
     */
    interface TaxSettingsUpdatedEvent extends EventBase {
      type: 'tax.settings.updated';
      data: TaxSettingsUpdatedEvent.Data;
    }

    namespace TaxSettingsUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Tax.Settings;

        previous_attributes?: Partial<Stripe.Tax.Settings>;
      }
    }

    /**
     * Occurs whenever a new tax rate is created.
     */
    interface TaxRateCreatedEvent extends EventBase {
      type: 'tax_rate.created';
      data: TaxRateCreatedEvent.Data;
    }

    namespace TaxRateCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.TaxRate;

        previous_attributes?: Partial<Stripe.TaxRate>;
      }
    }

    /**
     * Occurs whenever a tax rate is updated.
     */
    interface TaxRateUpdatedEvent extends EventBase {
      type: 'tax_rate.updated';
      data: TaxRateUpdatedEvent.Data;
    }

    namespace TaxRateUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.TaxRate;

        previous_attributes?: Partial<Stripe.TaxRate>;
      }
    }

    /**
     * Occurs whenever an action sent to a Terminal reader failed.
     */
    interface TerminalReaderActionFailedEvent extends EventBase {
      type: 'terminal.reader.action_failed';
      data: TerminalReaderActionFailedEvent.Data;
    }

    namespace TerminalReaderActionFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Terminal.Reader;

        previous_attributes?: Partial<Stripe.Terminal.Reader>;
      }
    }

    /**
     * Occurs whenever an action sent to a Terminal reader was successful.
     */
    interface TerminalReaderActionSucceededEvent extends EventBase {
      type: 'terminal.reader.action_succeeded';
      data: TerminalReaderActionSucceededEvent.Data;
    }

    namespace TerminalReaderActionSucceededEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Terminal.Reader;

        previous_attributes?: Partial<Stripe.Terminal.Reader>;
      }
    }

    /**
     * Occurs whenever an action sent to a Terminal reader is updated.
     */
    interface TerminalReaderActionUpdatedEvent extends EventBase {
      type: 'terminal.reader.action_updated';
      data: TerminalReaderActionUpdatedEvent.Data;
    }

    namespace TerminalReaderActionUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Terminal.Reader;

        previous_attributes?: Partial<Stripe.Terminal.Reader>;
      }
    }

    /**
     * Occurs whenever a test clock starts advancing.
     */
    interface TestHelpersTestClockAdvancingEvent extends EventBase {
      type: 'test_helpers.test_clock.advancing';
      data: TestHelpersTestClockAdvancingEvent.Data;
    }

    namespace TestHelpersTestClockAdvancingEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.TestHelpers.TestClock;

        previous_attributes?: Partial<Stripe.TestHelpers.TestClock>;
      }
    }

    /**
     * Occurs whenever a test clock is created.
     */
    interface TestHelpersTestClockCreatedEvent extends EventBase {
      type: 'test_helpers.test_clock.created';
      data: TestHelpersTestClockCreatedEvent.Data;
    }

    namespace TestHelpersTestClockCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.TestHelpers.TestClock;

        previous_attributes?: Partial<Stripe.TestHelpers.TestClock>;
      }
    }

    /**
     * Occurs whenever a test clock is deleted.
     */
    interface TestHelpersTestClockDeletedEvent extends EventBase {
      type: 'test_helpers.test_clock.deleted';
      data: TestHelpersTestClockDeletedEvent.Data;
    }

    namespace TestHelpersTestClockDeletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.TestHelpers.TestClock;

        previous_attributes?: Partial<Stripe.TestHelpers.TestClock>;
      }
    }

    /**
     * Occurs whenever a test clock fails to advance its frozen time.
     */
    interface TestHelpersTestClockInternalFailureEvent extends EventBase {
      type: 'test_helpers.test_clock.internal_failure';
      data: TestHelpersTestClockInternalFailureEvent.Data;
    }

    namespace TestHelpersTestClockInternalFailureEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.TestHelpers.TestClock;

        previous_attributes?: Partial<Stripe.TestHelpers.TestClock>;
      }
    }

    /**
     * Occurs whenever a test clock transitions to a ready status.
     */
    interface TestHelpersTestClockReadyEvent extends EventBase {
      type: 'test_helpers.test_clock.ready';
      data: TestHelpersTestClockReadyEvent.Data;
    }

    namespace TestHelpersTestClockReadyEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.TestHelpers.TestClock;

        previous_attributes?: Partial<Stripe.TestHelpers.TestClock>;
      }
    }

    /**
     * Occurs whenever a top-up is canceled.
     */
    interface TopupCanceledEvent extends EventBase {
      type: 'topup.canceled';
      data: TopupCanceledEvent.Data;
    }

    namespace TopupCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Topup;

        previous_attributes?: Partial<Stripe.Topup>;
      }
    }

    /**
     * Occurs whenever a top-up is created.
     */
    interface TopupCreatedEvent extends EventBase {
      type: 'topup.created';
      data: TopupCreatedEvent.Data;
    }

    namespace TopupCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Topup;

        previous_attributes?: Partial<Stripe.Topup>;
      }
    }

    /**
     * Occurs whenever a top-up fails.
     */
    interface TopupFailedEvent extends EventBase {
      type: 'topup.failed';
      data: TopupFailedEvent.Data;
    }

    namespace TopupFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Topup;

        previous_attributes?: Partial<Stripe.Topup>;
      }
    }

    /**
     * Occurs whenever a top-up is reversed.
     */
    interface TopupReversedEvent extends EventBase {
      type: 'topup.reversed';
      data: TopupReversedEvent.Data;
    }

    namespace TopupReversedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Topup;

        previous_attributes?: Partial<Stripe.Topup>;
      }
    }

    /**
     * Occurs whenever a top-up succeeds.
     */
    interface TopupSucceededEvent extends EventBase {
      type: 'topup.succeeded';
      data: TopupSucceededEvent.Data;
    }

    namespace TopupSucceededEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Topup;

        previous_attributes?: Partial<Stripe.Topup>;
      }
    }

    /**
     * Occurs whenever a transfer is created.
     */
    interface TransferCreatedEvent extends EventBase {
      type: 'transfer.created';
      data: TransferCreatedEvent.Data;
    }

    namespace TransferCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Transfer;

        previous_attributes?: Partial<Stripe.Transfer>;
      }
    }

    /**
     * Occurs whenever a transfer is reversed, including partial reversals.
     */
    interface TransferReversedEvent extends EventBase {
      type: 'transfer.reversed';
      data: TransferReversedEvent.Data;
    }

    namespace TransferReversedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Transfer;

        previous_attributes?: Partial<Stripe.Transfer>;
      }
    }

    /**
     * Occurs whenever a transfer's description or metadata is updated.
     */
    interface TransferUpdatedEvent extends EventBase {
      type: 'transfer.updated';
      data: TransferUpdatedEvent.Data;
    }

    namespace TransferUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Transfer;

        previous_attributes?: Partial<Stripe.Transfer>;
      }
    }

    /**
     * Occurs whenever an CreditReversal is submitted and created.
     */
    interface TreasuryCreditReversalCreatedEvent extends EventBase {
      type: 'treasury.credit_reversal.created';
      data: TreasuryCreditReversalCreatedEvent.Data;
    }

    namespace TreasuryCreditReversalCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.CreditReversal;

        previous_attributes?: Partial<Stripe.Treasury.CreditReversal>;
      }
    }

    /**
     * Occurs whenever an CreditReversal post is posted.
     */
    interface TreasuryCreditReversalPostedEvent extends EventBase {
      type: 'treasury.credit_reversal.posted';
      data: TreasuryCreditReversalPostedEvent.Data;
    }

    namespace TreasuryCreditReversalPostedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.CreditReversal;

        previous_attributes?: Partial<Stripe.Treasury.CreditReversal>;
      }
    }

    /**
     * Occurs whenever a DebitReversal is completed.
     */
    interface TreasuryDebitReversalCompletedEvent extends EventBase {
      type: 'treasury.debit_reversal.completed';
      data: TreasuryDebitReversalCompletedEvent.Data;
    }

    namespace TreasuryDebitReversalCompletedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.DebitReversal;

        previous_attributes?: Partial<Stripe.Treasury.DebitReversal>;
      }
    }

    /**
     * Occurs whenever a DebitReversal is created.
     */
    interface TreasuryDebitReversalCreatedEvent extends EventBase {
      type: 'treasury.debit_reversal.created';
      data: TreasuryDebitReversalCreatedEvent.Data;
    }

    namespace TreasuryDebitReversalCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.DebitReversal;

        previous_attributes?: Partial<Stripe.Treasury.DebitReversal>;
      }
    }

    /**
     * Occurs whenever an initial credit is granted on a DebitReversal.
     */
    interface TreasuryDebitReversalInitialCreditGrantedEvent extends EventBase {
      type: 'treasury.debit_reversal.initial_credit_granted';
      data: TreasuryDebitReversalInitialCreditGrantedEvent.Data;
    }

    namespace TreasuryDebitReversalInitialCreditGrantedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.DebitReversal;

        previous_attributes?: Partial<Stripe.Treasury.DebitReversal>;
      }
    }

    /**
     * Occurs whenever the status of the FinancialAccount becomes closed.
     */
    interface TreasuryFinancialAccountClosedEvent extends EventBase {
      type: 'treasury.financial_account.closed';
      data: TreasuryFinancialAccountClosedEvent.Data;
    }

    namespace TreasuryFinancialAccountClosedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.FinancialAccount;

        previous_attributes?: Partial<Stripe.Treasury.FinancialAccount>;
      }
    }

    /**
     * Occurs whenever a new FinancialAccount is created.
     */
    interface TreasuryFinancialAccountCreatedEvent extends EventBase {
      type: 'treasury.financial_account.created';
      data: TreasuryFinancialAccountCreatedEvent.Data;
    }

    namespace TreasuryFinancialAccountCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.FinancialAccount;

        previous_attributes?: Partial<Stripe.Treasury.FinancialAccount>;
      }
    }

    /**
     * Occurs whenever the statuses of any features within an existing FinancialAccount are updated.
     */
    interface TreasuryFinancialAccountFeaturesStatusUpdatedEvent
      extends EventBase {
      type: 'treasury.financial_account.features_status_updated';
      data: TreasuryFinancialAccountFeaturesStatusUpdatedEvent.Data;
    }

    namespace TreasuryFinancialAccountFeaturesStatusUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.FinancialAccount;

        previous_attributes?: Partial<Stripe.Treasury.FinancialAccount>;
      }
    }

    /**
     * Occurs whenever an InboundTransfer is canceled.
     */
    interface TreasuryInboundTransferCanceledEvent extends EventBase {
      type: 'treasury.inbound_transfer.canceled';
      data: TreasuryInboundTransferCanceledEvent.Data;
    }

    namespace TreasuryInboundTransferCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.InboundTransfer;

        previous_attributes?: Partial<Stripe.Treasury.InboundTransfer>;
      }
    }

    /**
     * Occurs whenever an InboundTransfer is created.
     */
    interface TreasuryInboundTransferCreatedEvent extends EventBase {
      type: 'treasury.inbound_transfer.created';
      data: TreasuryInboundTransferCreatedEvent.Data;
    }

    namespace TreasuryInboundTransferCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.InboundTransfer;

        previous_attributes?: Partial<Stripe.Treasury.InboundTransfer>;
      }
    }

    /**
     * Occurs whenever an InboundTransfer has failed.
     */
    interface TreasuryInboundTransferFailedEvent extends EventBase {
      type: 'treasury.inbound_transfer.failed';
      data: TreasuryInboundTransferFailedEvent.Data;
    }

    namespace TreasuryInboundTransferFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.InboundTransfer;

        previous_attributes?: Partial<Stripe.Treasury.InboundTransfer>;
      }
    }

    /**
     * Occurs whenever an InboundTransfer has succeeded.
     */
    interface TreasuryInboundTransferSucceededEvent extends EventBase {
      type: 'treasury.inbound_transfer.succeeded';
      data: TreasuryInboundTransferSucceededEvent.Data;
    }

    namespace TreasuryInboundTransferSucceededEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.InboundTransfer;

        previous_attributes?: Partial<Stripe.Treasury.InboundTransfer>;
      }
    }

    /**
     * Occurs whenever an OutboundPayment is canceled.
     */
    interface TreasuryOutboundPaymentCanceledEvent extends EventBase {
      type: 'treasury.outbound_payment.canceled';
      data: TreasuryOutboundPaymentCanceledEvent.Data;
    }

    namespace TreasuryOutboundPaymentCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundPayment;

        previous_attributes?: Partial<Stripe.Treasury.OutboundPayment>;
      }
    }

    /**
     * Occurs whenever a new OutboundPayment is successfully created.
     */
    interface TreasuryOutboundPaymentCreatedEvent extends EventBase {
      type: 'treasury.outbound_payment.created';
      data: TreasuryOutboundPaymentCreatedEvent.Data;
    }

    namespace TreasuryOutboundPaymentCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundPayment;

        previous_attributes?: Partial<Stripe.Treasury.OutboundPayment>;
      }
    }

    /**
     * Occurs whenever the arrival date on an OutboundPayment updates.
     */
    interface TreasuryOutboundPaymentExpectedArrivalDateUpdatedEvent
      extends EventBase {
      type: 'treasury.outbound_payment.expected_arrival_date_updated';
      data: TreasuryOutboundPaymentExpectedArrivalDateUpdatedEvent.Data;
    }

    namespace TreasuryOutboundPaymentExpectedArrivalDateUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundPayment;

        previous_attributes?: Partial<Stripe.Treasury.OutboundPayment>;
      }
    }

    /**
     * Occurs whenever an OutboundPayment fails.
     */
    interface TreasuryOutboundPaymentFailedEvent extends EventBase {
      type: 'treasury.outbound_payment.failed';
      data: TreasuryOutboundPaymentFailedEvent.Data;
    }

    namespace TreasuryOutboundPaymentFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundPayment;

        previous_attributes?: Partial<Stripe.Treasury.OutboundPayment>;
      }
    }

    /**
     * Occurs whenever an OutboundPayment posts.
     */
    interface TreasuryOutboundPaymentPostedEvent extends EventBase {
      type: 'treasury.outbound_payment.posted';
      data: TreasuryOutboundPaymentPostedEvent.Data;
    }

    namespace TreasuryOutboundPaymentPostedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundPayment;

        previous_attributes?: Partial<Stripe.Treasury.OutboundPayment>;
      }
    }

    /**
     * Occurs whenever an OutboundPayment was returned.
     */
    interface TreasuryOutboundPaymentReturnedEvent extends EventBase {
      type: 'treasury.outbound_payment.returned';
      data: TreasuryOutboundPaymentReturnedEvent.Data;
    }

    namespace TreasuryOutboundPaymentReturnedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundPayment;

        previous_attributes?: Partial<Stripe.Treasury.OutboundPayment>;
      }
    }

    /**
     * Occurs whenever tracking_details on an OutboundPayment is updated.
     */
    interface TreasuryOutboundPaymentTrackingDetailsUpdatedEvent
      extends EventBase {
      type: 'treasury.outbound_payment.tracking_details_updated';
      data: TreasuryOutboundPaymentTrackingDetailsUpdatedEvent.Data;
    }

    namespace TreasuryOutboundPaymentTrackingDetailsUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundPayment;

        previous_attributes?: Partial<Stripe.Treasury.OutboundPayment>;
      }
    }

    /**
     * Occurs whenever an OutboundTransfer is canceled.
     */
    interface TreasuryOutboundTransferCanceledEvent extends EventBase {
      type: 'treasury.outbound_transfer.canceled';
      data: TreasuryOutboundTransferCanceledEvent.Data;
    }

    namespace TreasuryOutboundTransferCanceledEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundTransfer;

        previous_attributes?: Partial<Stripe.Treasury.OutboundTransfer>;
      }
    }

    /**
     * Occurs whenever an OutboundTransfer is created.
     */
    interface TreasuryOutboundTransferCreatedEvent extends EventBase {
      type: 'treasury.outbound_transfer.created';
      data: TreasuryOutboundTransferCreatedEvent.Data;
    }

    namespace TreasuryOutboundTransferCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundTransfer;

        previous_attributes?: Partial<Stripe.Treasury.OutboundTransfer>;
      }
    }

    /**
     * Occurs whenever the arrival date on an OutboundTransfer updates.
     */
    interface TreasuryOutboundTransferExpectedArrivalDateUpdatedEvent
      extends EventBase {
      type: 'treasury.outbound_transfer.expected_arrival_date_updated';
      data: TreasuryOutboundTransferExpectedArrivalDateUpdatedEvent.Data;
    }

    namespace TreasuryOutboundTransferExpectedArrivalDateUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundTransfer;

        previous_attributes?: Partial<Stripe.Treasury.OutboundTransfer>;
      }
    }

    /**
     * Occurs whenever an OutboundTransfer has failed.
     */
    interface TreasuryOutboundTransferFailedEvent extends EventBase {
      type: 'treasury.outbound_transfer.failed';
      data: TreasuryOutboundTransferFailedEvent.Data;
    }

    namespace TreasuryOutboundTransferFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundTransfer;

        previous_attributes?: Partial<Stripe.Treasury.OutboundTransfer>;
      }
    }

    /**
     * Occurs whenever an OutboundTransfer is posted.
     */
    interface TreasuryOutboundTransferPostedEvent extends EventBase {
      type: 'treasury.outbound_transfer.posted';
      data: TreasuryOutboundTransferPostedEvent.Data;
    }

    namespace TreasuryOutboundTransferPostedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundTransfer;

        previous_attributes?: Partial<Stripe.Treasury.OutboundTransfer>;
      }
    }

    /**
     * Occurs whenever an OutboundTransfer is returned.
     */
    interface TreasuryOutboundTransferReturnedEvent extends EventBase {
      type: 'treasury.outbound_transfer.returned';
      data: TreasuryOutboundTransferReturnedEvent.Data;
    }

    namespace TreasuryOutboundTransferReturnedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundTransfer;

        previous_attributes?: Partial<Stripe.Treasury.OutboundTransfer>;
      }
    }

    /**
     * Occurs whenever tracking_details on an OutboundTransfer is updated.
     */
    interface TreasuryOutboundTransferTrackingDetailsUpdatedEvent
      extends EventBase {
      type: 'treasury.outbound_transfer.tracking_details_updated';
      data: TreasuryOutboundTransferTrackingDetailsUpdatedEvent.Data;
    }

    namespace TreasuryOutboundTransferTrackingDetailsUpdatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.OutboundTransfer;

        previous_attributes?: Partial<Stripe.Treasury.OutboundTransfer>;
      }
    }

    /**
     * Occurs whenever a received_credit is created as a result of funds being pushed by another account.
     */
    interface TreasuryReceivedCreditCreatedEvent extends EventBase {
      type: 'treasury.received_credit.created';
      data: TreasuryReceivedCreditCreatedEvent.Data;
    }

    namespace TreasuryReceivedCreditCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.ReceivedCredit;

        previous_attributes?: Partial<Stripe.Treasury.ReceivedCredit>;
      }
    }

    /**
     * Occurs whenever a received_credit transitions to failed state. Only applicable for check deposits.
     */
    interface TreasuryReceivedCreditFailedEvent extends EventBase {
      type: 'treasury.received_credit.failed';
      data: TreasuryReceivedCreditFailedEvent.Data;
    }

    namespace TreasuryReceivedCreditFailedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.ReceivedCredit;

        previous_attributes?: Partial<Stripe.Treasury.ReceivedCredit>;
      }
    }

    /**
     * Occurs whenever a received_credit transitions to succeeded state. Only applicable for check deposits.
     */
    interface TreasuryReceivedCreditSucceededEvent extends EventBase {
      type: 'treasury.received_credit.succeeded';
      data: TreasuryReceivedCreditSucceededEvent.Data;
    }

    namespace TreasuryReceivedCreditSucceededEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.ReceivedCredit;

        previous_attributes?: Partial<Stripe.Treasury.ReceivedCredit>;
      }
    }

    /**
     * Occurs whenever a received_debit is created as a result of funds being pulled by another account.
     */
    interface TreasuryReceivedDebitCreatedEvent extends EventBase {
      type: 'treasury.received_debit.created';
      data: TreasuryReceivedDebitCreatedEvent.Data;
    }

    namespace TreasuryReceivedDebitCreatedEvent {
      interface Data extends Stripe.Event.Data {
        object: Stripe.Treasury.ReceivedDebit;

        previous_attributes?: Partial<Stripe.Treasury.ReceivedDebit>;
      }
    }
  }
}
